// src/app/auth/reset-password/page.tsx
'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'react-hot-toast';
import { <PERSON>Lock, <PERSON>C<PERSON>ck, FiClock } from 'react-icons/fi';

function ResetPasswordForm() {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSessionReady, setIsSessionReady] = useState(false);
  const [linkError, setLinkError] = useState<string | null>(null);

  useEffect(() => {
    console.log('🔐 [ResetPassword] Component mounted, checking URL:', window.location.href);
    console.log('🔐 [ResetPassword] URL breakdown:', {
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      origin: window.location.origin
    });

    // Verificar si hay errores en la URL
    const urlParams = new URLSearchParams(window.location.search);
    const urlError = urlParams.get('error');
    const errorCode = urlParams.get('error_code');
    const errorDescription = urlParams.get('error_description');

    console.log('🔐 [ResetPassword] URL parameters check:', {
      urlError,
      errorCode,
      errorDescription
    });

    if (urlError) {
      console.log('🔐 [ResetPassword] URL contains error:', { urlError, errorCode, errorDescription });

      let userFriendlyMessage = 'Error en el enlace de recuperación.';

      if (errorCode === 'otp_expired' || urlError === 'access_denied') {
        userFriendlyMessage = 'El enlace de recuperación ha expirado o ya fue utilizado. Por favor, solicita un nuevo enlace.';
      } else if (errorDescription) {
        userFriendlyMessage = decodeURIComponent(errorDescription);
      }

      setLinkError(userFriendlyMessage);
      return; // No continuar con la verificación de sesión si hay error
    }

    const supabase = createClient();

    // Verificar inmediatamente si ya hay una sesión de recuperación
    const checkSession = async () => {
      try {
        console.log('🔐 [ResetPassword] Checking session...');
        console.log('🔐 [ResetPassword] Current URL hash:', window.location.hash);
        console.log('🔐 [ResetPassword] Current URL search:', window.location.search);

        // NUEVO: Procesar manualmente el hash si detectSessionInUrl no funciona
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');
        const tokenType = hashParams.get('type');

        console.log('🔐 [ResetPassword] Hash parameters:', {
          accessToken: accessToken ? accessToken.substring(0, 10) + "..." : null,
          refreshToken: refreshToken ? refreshToken.substring(0, 10) + "..." : null,
          tokenType,
          fullHash: window.location.hash,
          allHashParams: Object.fromEntries(hashParams.entries())
        });

        // Si hay un access_token en el hash, intentar establecer la sesión manualmente
        if (accessToken && tokenType === 'recovery') {
          console.log('🔐 [ResetPassword] Found recovery token in hash, attempting to set session...');

          if (!refreshToken) {
            console.warn('⚠️🔐 [ResetPassword] No refresh_token found in hash. This might cause setSession to fail.');
            // Para recovery tokens, a veces no hay refresh_token, intentemos de todas formas
          }

          try {
            const sessionData = {
              access_token: accessToken,
              refresh_token: refreshToken || '' // Usar string vacío si no hay refresh_token
            };

            console.log('🔐 [ResetPassword] Attempting setSession with:', {
              hasAccessToken: !!sessionData.access_token,
              hasRefreshToken: !!sessionData.refresh_token,
              accessTokenLength: sessionData.access_token.length,
              refreshTokenLength: sessionData.refresh_token.length
            });

            const { data, error: setSessionError } = await supabase.auth.setSession(sessionData);

            console.log('🔐 [ResetPassword] setSession result:', {
              hasSession: !!data.session,
              userId: data.session?.user?.id,
              userEmail: data.session?.user?.email,
              error: setSessionError?.message,
              errorCode: setSessionError?.['code'] || null
            });

            if (data.session && !setSessionError) {
              console.log('✅🔐 [ResetPassword] Session established manually via setSession');
              setIsSessionReady(true);
              return; // Salir temprano si el setSession funcionó
            } else if (setSessionError) {
              console.error('❌🔐 [ResetPassword] setSession failed:', setSessionError);
              // Continuar con el fallback getSession
            }
          } catch (setSessionErr) {
            console.error('❌🔐 [ResetPassword] Exception in setSession:', setSessionErr);
          }
        }

        // Fallback 1: intentar getSession normal
        const { data: { session }, error } = await supabase.auth.getSession();
        console.log('🔐 [ResetPassword] getSession result (DETAILED):', {
          hasSession: !!session,
          userId: session?.user?.id,
          userEmail: session?.user?.email,
          userAud: session?.user?.aud,
          sessionExpiresAt: session?.expires_at,
          tokenPreview: session?.access_token?.substring(0, 10) + "..." || null,
          refreshTokenPreview: session?.refresh_token?.substring(0, 10) + "..." || null,
          error: error?.message
        });

        if (session && session.user) {
          console.log('✅🔐 [ResetPassword] Session detected via getSession:', session.user.id);
          console.log('✅🔐 [ResetPassword] Setting isSessionReady to TRUE');
          setIsSessionReady(true);
          return;
        }

        // Fallback 2: Si tenemos access_token pero no sesión, intentar getUser directamente
        if (accessToken && tokenType === 'recovery') {
          console.log('🔐 [ResetPassword] Trying getUser as final fallback...');
          try {
            const { data: { user }, error: getUserError } = await supabase.auth.getUser(accessToken);
            console.log('🔐 [ResetPassword] getUser result:', {
              hasUser: !!user,
              userId: user?.id,
              userEmail: user?.email,
              error: getUserError?.message
            });

            if (user && !getUserError) {
              console.log('✅🔐 [ResetPassword] User detected via getUser, assuming recovery session');
              setIsSessionReady(true);
              return;
            }
          } catch (getUserErr) {
            console.error('❌🔐 [ResetPassword] Exception in getUser:', getUserErr);
          }
        }

        console.log('❌🔐 [ResetPassword] No session found in any method');
        // Si llegamos aquí y hay un token, podría ser un problema de configuración
        if (accessToken) {
          console.warn('⚠️🔐 [ResetPassword] Token present but no session established. Possible configuration issue.');
        }
      } catch (error) {
        console.error('❌🔐 [ResetPassword] Error checking session:', error);
      }
    };

    checkSession();

    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔐🔑 [ResetPasswordForm] Raw Auth state change:', { // Log MÁS DETALLADO
        event,
        hasSession: !!session,
        userId: session?.user?.id,
        userEmail: session?.user?.email,
        userAud: session?.user?.aud,
        sessionExpiresAt: session?.expires_at,
        userMetadata: session?.user?.user_metadata || null, // Metadata del usuario
        tokenPreview: session?.access_token?.substring(0, 10) + "..." || null // Preview del token por seguridad
      });

      if (event === 'PASSWORD_RECOVERY') {
        console.log('✅🔐 [ResetPasswordForm] PASSWORD_RECOVERY event received!', session);
        if (session) {
          setIsSessionReady(true);
          console.log('✅🔐 [ResetPasswordForm] isSessionReady set to TRUE.');
        } else {
          console.warn('⚠️🔐 [ResetPasswordForm] PASSWORD_RECOVERY event received but session is null.');
          setLinkError('Error procesando el enlace. No se pudo establecer la sesión de recuperación.');
        }
      } else if (event === 'SIGNED_IN' && session) {
        console.log('✅🔐 [ResetPasswordForm] SIGNED_IN event received with session!');
        setIsSessionReady(true);
        console.log('✅🔐 [ResetPasswordForm] isSessionReady set to TRUE via SIGNED_IN.');
      } else if (event === 'INITIAL_SESSION') {
        console.log('🔄🔐 [ResetPasswordForm] INITIAL_SESSION event received');
        if (session) {
          console.log('✅🔐 [ResetPasswordForm] INITIAL_SESSION has session, setting ready to TRUE');
          setIsSessionReady(true);
        } else {
          console.log('❌🔐 [ResetPasswordForm] INITIAL_SESSION has no session');
        }
      } else if (event === 'SIGNED_OUT') {
        console.log('🚪🔐 [ResetPasswordForm] SIGNED_OUT event received');
        setIsSessionReady(false);
      } else {
        console.log(`🔍🔐 [ResetPasswordForm] Other auth event: ${event}`, { hasSession: !!session });
      }
    });

    return () => {
      console.log('🔐 [ResetPassword] Cleaning up auth listener');
      authListener?.subscription.unsubscribe();
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (password.length < 6) {
      setError('La nueva contraseña debe tener al menos 6 caracteres.');
      return;
    }
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return;
    }

    setLoading(true);
    const supabase = createClient();
    const { error: updateError } = await supabase.auth.updateUser({ password });
    setLoading(false);

    if (updateError) {
      setError(updateError.message);
      toast.error('Error al actualizar la contraseña.');
    } else {
      toast.success('¡Contraseña actualizada! Por favor, inicia sesión de nuevo.');
      // Cerrar la sesión actual (de recuperación) y redirigir a login
      await supabase.auth.signOut();
      router.push('/login');
    }
  };

  // Si hay un error en el enlace, mostrar mensaje de error
  if (linkError) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <FiClock className="w-6 h-6 text-red-600" />
            </div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Enlace Expirado
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            El enlace de recuperación no es válido
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-700 text-sm">{linkError}</p>
              </div>

              <button
                onClick={() => router.push('/profile')}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Volver al Perfil
              </button>

              <p className="mt-4 text-xs text-gray-500">
                Desde tu perfil puedes solicitar un nuevo enlace de recuperación
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isSessionReady) {
    return (
      <div className="flex flex-col justify-center items-center h-screen bg-gray-50 text-center p-4">
        <FiClock className="w-12 h-12 text-gray-400 mb-4 animate-pulse" />
        <h2 className="text-xl font-medium text-gray-700">Verificando tu enlace de recuperación...</h2>
        <p className="text-gray-500 mt-2">Esta página se activará si accedes desde un enlace válido enviado a tu email.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <FiLock className="w-12 h-12 text-blue-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Crea tu Nueva Contraseña
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Introduce una nueva contraseña segura para tu cuenta
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Nueva Contraseña
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Mínimo 6 caracteres"
                />
              </div>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirmar Nueva Contraseña
              </label>
              <div className="mt-1">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Repite la contraseña"
                />
              </div>
            </div>

            {error && (
              <div className="text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200">
                {error}
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Actualizando...
                  </>
                ) : (
                  <>
                    <FiCheck className="w-4 h-4 mr-2" />
                    Actualizar Contraseña
                  </>
                )}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Consejos de seguridad</span>
              </div>
            </div>

            <div className="mt-4 text-xs text-gray-600 space-y-1">
              <p>• Usa al menos 6 caracteres</p>
              <p>• Combina letras, números y símbolos</p>
              <p>• No uses información personal</p>
              <p>• No reutilices contraseñas de otras cuentas</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  );
}
