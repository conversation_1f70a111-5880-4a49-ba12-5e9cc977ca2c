import React, { useState, useEffect } from 'react';
import {
  FiBook,
  FiFileText,
  FiCheckSquare,
  FiClock,
  FiTrendingUp,
  FiTarget,
  FiPlay,
  FiPlus,
  FiCalendar,
  FiAward,
  FiBookOpen,
  FiLayers
} from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';
import { EstadisticasDashboard, ProximasFlashcards, obtenerEstadisticasDashboard, obtenerProximasFlashcards } from '@/lib/supabase/dashboardService';
import { tieneTemarioConfigurado } from '@/features/temario/services/temarioService';
import TemarioSetup from '@/features/temario/components/TemarioSetup';

interface DashboardProps {
  onNavigateToTab: (tab: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNavigateToTab }) => {
  const { user } = useAuth();
  const [estadisticas, setEstadisticas] = useState<EstadisticasDashboard | null>(null);
  const [proximasFlashcards, setProximasFlashcards] = useState<ProximasFlashcards[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [mostrarSetupTemario, setMostrarSetupTemario] = useState(false);

  useEffect(() => {
    cargarDatos();
  }, []);

  const cargarDatos = async () => {
    setIsLoading(true);
    try {
      const [statsData, proximasData, temarioConfigurado] = await Promise.all([
        obtenerEstadisticasDashboard(),
        obtenerProximasFlashcards(5),
        tieneTemarioConfigurado()
      ]);

      setEstadisticas(statsData);
      setProximasFlashcards(proximasData);

      // Si no tiene temario configurado, mostrar setup automáticamente
      if (!temarioConfigurado) {
        setMostrarSetupTemario(true);
      }
    } catch (error) {
      console.error('Error al cargar datos del dashboard:', error);
      // En caso de error, usar datos por defecto
      setEstadisticas({
        totalDocumentos: 0,
        totalColeccionesFlashcards: 0,
        totalTests: 0,
        totalFlashcards: 0,
        flashcardsParaHoy: 0,
        flashcardsNuevas: 0,
        flashcardsAprendiendo: 0,
        flashcardsRepasando: 0,
        testsRealizados: 0,
        porcentajeAcierto: 0,
        coleccionesRecientes: [],
        testsRecientes: []
      });
      setProximasFlashcards([]);
      setMostrarSetupTemario(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTemarioSetupComplete = () => {
    setMostrarSetupTemario(false);
    // Recargar datos del dashboard
    cargarDatos();
  };

  const obtenerSaludo = () => {
    const hora = new Date().getHours();
    if (hora < 12) return 'Buenos días';
    if (hora < 18) return 'Buenas tardes';
    return 'Buenas noches';
  };

  const obtenerNombreUsuario = () => {
    return user?.email?.split('@')[0] || 'Estudiante';
  };

  // Mostrar setup de temario si es necesario
  if (mostrarSetupTemario) {
    return <TemarioSetup onComplete={handleTemarioSetupComplete} />;
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Saludo personalizado */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          {obtenerSaludo()}, {obtenerNombreUsuario()}! 👋
        </h1>
        <p className="text-blue-100">
          ¿Listo para continuar con tu preparación? Aquí tienes un resumen de tu progreso.
        </p>
      </div>

      {/* Estadísticas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Documentos</p>
              <p className="text-2xl font-bold text-gray-900">{estadisticas?.totalDocumentos || 0}</p>
            </div>
            <FiFileText className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Colecciones</p>
              <p className="text-2xl font-bold text-gray-900">{estadisticas?.totalColeccionesFlashcards || 0}</p>
            </div>
            <FiBook className="h-8 w-8 text-emerald-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Tests</p>
              <p className="text-2xl font-bold text-gray-900">{estadisticas?.totalTests || 0}</p>
            </div>
            <FiCheckSquare className="h-8 w-8 text-pink-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Flashcards</p>
              <p className="text-2xl font-bold text-gray-900">{estadisticas?.totalFlashcards || 0}</p>
            </div>
            <FiTarget className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Estudio de hoy */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Estudio de Hoy</h2>
          <FiCalendar className="h-6 w-6 text-gray-400" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-800">Para Repasar Hoy</p>
                <p className="text-2xl font-bold text-orange-600">{estadisticas?.flashcardsParaHoy || 0}</p>
              </div>
              <FiClock className="h-6 w-6 text-orange-600" />
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-800">Nuevas</p>
                <p className="text-2xl font-bold text-blue-600">{estadisticas?.flashcardsNuevas || 0}</p>
              </div>
              <FiBookOpen className="h-6 w-6 text-blue-600" />
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-800">% Acierto Tests</p>
                <p className="text-2xl font-bold text-green-600">{estadisticas?.porcentajeAcierto.toFixed(1) || 0}%</p>
              </div>
              <FiTrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        {estadisticas && estadisticas.flashcardsParaHoy > 0 && (
          <div className="mt-4">
            <button
              onClick={() => onNavigateToTab('misFlashcards')}
              className="bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors"
            >
              <FiPlay className="mr-2" />
              Comenzar Estudio
            </button>
          </div>
        )}
      </div>

      {/* Próximas flashcards */}
      {proximasFlashcards.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Próximas Flashcards</h2>
          <div className="space-y-3">
            {proximasFlashcards.slice(0, 3).map((flashcard) => (
              <div key={flashcard.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-900 truncate">{flashcard.pregunta}</p>
                  <p className="text-sm text-gray-600">{flashcard.coleccionTitulo}</p>
                </div>
                <div className="text-right">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    flashcard.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' :
                    flashcard.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' :
                    flashcard.estado === 'repasando' ? 'bg-orange-100 text-orange-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {flashcard.estado}
                  </span>
                </div>
              </div>
            ))}
          </div>
          {proximasFlashcards.length > 3 && (
            <button
              onClick={() => onNavigateToTab('misFlashcards')}
              className="mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Ver todas las flashcards pendientes
            </button>
          )}
        </div>
      )}

      {/* Actividad reciente */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Colecciones recientes */}
        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Colecciones Recientes</h2>
          <div className="space-y-3">
            {estadisticas?.coleccionesRecientes.map((coleccion) => (
              <div key={coleccion.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{coleccion.titulo}</p>
                  <p className="text-sm text-gray-600">
                    Creada: {new Date(coleccion.fechaCreacion).toLocaleDateString('es-ES')}
                  </p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    {coleccion.paraHoy} para hoy
                  </span>
                </div>
              </div>
            ))}
          </div>
          <button
            onClick={() => onNavigateToTab('misFlashcards')}
            className="mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium"
          >
            Ver todas las colecciones
          </button>
        </div>

        {/* Tests recientes */}
        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Tests Recientes</h2>
          <div className="space-y-3">
            {estadisticas?.testsRecientes.map((test) => (
              <div key={test.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{test.titulo}</p>
                  <p className="text-sm text-gray-600">
                    Creado: {new Date(test.fechaCreacion).toLocaleDateString('es-ES')}
                  </p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                    {test.numeroPreguntas} preguntas
                  </span>
                </div>
              </div>
            ))}
          </div>
          <button
            onClick={() => onNavigateToTab('misTests')}
            className="mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium"
          >
            Ver todos los tests
          </button>
        </div>
      </div>

      {/* Acciones rápidas */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Acciones Rápidas</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => onNavigateToTab('preguntas')}
            className="flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200"
          >
            <FiFileText className="h-6 w-6 text-blue-600 mr-3" />
            <span className="font-medium text-blue-900">Hacer Preguntas</span>
          </button>

          <button
            onClick={() => onNavigateToTab('flashcards')}
            className="flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200"
          >
            <FiPlus className="h-6 w-6 text-orange-600 mr-3" />
            <span className="font-medium text-orange-900">Crear Flashcards</span>
          </button>

          <button
            onClick={() => onNavigateToTab('tests')}
            className="flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200"
          >
            <FiCheckSquare className="h-6 w-6 text-indigo-600 mr-3" />
            <span className="font-medium text-indigo-900">Generar Tests</span>
          </button>

          <button
            onClick={() => onNavigateToTab('mapas')}
            className="flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200"
          >
            <FiLayers className="h-6 w-6 text-purple-600 mr-3" />
            <span className="font-medium text-purple-900">Mapas Mentales</span>
          </button>

          <button
            onClick={() => onNavigateToTab('planEstudios')}
            className="flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200"
          >
            <FiCalendar className="h-6 w-6 text-teal-600 mr-3" />
            <span className="font-medium text-teal-900">Plan de Estudios</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
