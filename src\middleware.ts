// src/middleware.ts
// Middleware robusto de seguridad para OposiAI

import { createServerClient } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';

// Configuración de rutas y permisos
const ROUTE_PERMISSIONS = {
  // Rutas públicas (no requieren autenticación)
  public: [
    '/',
    '/login',
    '/payment',
    '/thank-you',
    '/contact',
    '/privacy',
    '/terms',
    '/auth/callback',
    '/auth/unauthorized',
    '/auth/reset-password',
    '/api/stripe/webhook',
    '/api/stripe/create-checkout-session',
    '/api/notify-signup',
    '/api/user/status'
  ],

  // Rutas que requieren autenticación básica
  authenticated: [
    '/app',
    '/dashboard',
    '/profile',
    '/welcome',
    '/upgrade-plan'
  ],

  // Rutas que requieren planes específicos
  planRestricted: {
    '/plan-estudios': ['pro'],
    '/app/ai-tutor': ['usuario', 'pro'],
    '/app/summaries': ['pro'],
    '/app/advanced-features': ['pro']
  }
};

// Configuración de seguridad
const SECURITY_CONFIG = {
  enableStrictValidation: process.env.STRICT_PLAN_VALIDATION === 'true',
  requirePaymentVerification: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',
  enableAccessLogging: process.env.ENABLE_ACCESS_LOGGING === 'true',
  sessionTimeout: 5 * 60 * 1000, // 5 minutos en millisegundos
};

export async function middleware(request: NextRequest) {
  const startTime = Date.now();
  const { pathname } = request.nextUrl;

  try {
    let supabaseResponse = NextResponse.next({
      request,
    });

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
          detectSessionInUrl: false
        },
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            const filteredCookies = cookiesToSet.filter(cookie =>
              !cookie.name.includes('auth-token') &&
              !cookie.name.includes('refresh-token')
            );

            filteredCookies.forEach(({ name, value, options }) => request.cookies.set(name, value));
            supabaseResponse = NextResponse.next({
              request,
            });
            filteredCookies.forEach(({ name, value, options }) =>
              supabaseResponse.cookies.set(name, value, {
                ...options,
                maxAge: undefined,
                expires: undefined
              })
            );
          },
        },
      }
    );

    // Log de acceso si está habilitado
    if (SECURITY_CONFIG.enableAccessLogging) {
      console.log(`🔍 [MIDDLEWARE] ${request.method} ${pathname}`);
    }

    // Verificar si es una ruta pública
    if (isPublicRoute(pathname)) {
      return addSecurityHeaders(supabaseResponse);
    }

    // IMPORTANT: DO NOT REMOVE auth.getUser()
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    // Si no hay usuario y la ruta requiere autenticación
    if (!user || authError) {
      if (SECURITY_CONFIG.enableAccessLogging) {
        console.log(`❌ [MIDDLEWARE] Unauthorized access attempt to ${pathname}`);
      }

      return redirectToLogin(request);
    }

    // Para rutas autenticadas básicas, verificar perfil
    if (isAuthenticatedRoute(pathname)) {
      const profileValidation = await validateUserProfile(user.id, supabase);

      if (!profileValidation.valid) {
        if (SECURITY_CONFIG.enableAccessLogging) {
          console.log(`❌ [MIDDLEWARE] Profile validation failed: ${profileValidation.reason}`);
        }

        // Manejar usuarios autenticados sin perfil
        if (profileValidation.reason === 'Profile not found') {
          // Verificar si es un usuario que se registró para cuenta gratuita
          const isLegitimateUser = await checkIfLegitimateUser(user, supabase);

          if (isLegitimateUser.isLegitimate) {
            try {
              const accountType = isLegitimateUser.accountType === 'unknown' ? 'free' : isLegitimateUser.accountType;
              await createRecoveryProfile(user.id, user.email, accountType, supabase);
              console.log(`✅ [MIDDLEWARE] Perfil de recuperación creado para usuario legítimo: ${user.id}`);
              // Continuar con la ejecución normal
            } catch (error) {
              console.error('❌ [MIDDLEWARE] Error creando perfil de recuperación:', error);
              return redirectToPayment(request, 'Profile recovery failed');
            }
          } else {
            // Usuario sospechoso sin perfil válido
            console.log(`🚨 [MIDDLEWARE] Usuario sospechoso sin perfil: ${user.id}`);
            return redirectToPayment(request, 'Invalid account - please complete registration');
          }
        } else {
          // Para otros errores de validación (cuenta expirada, pago no verificado, etc.)
          return redirectToPayment(request, profileValidation.reason);
        }
      }
    }

    // Para rutas con restricciones de plan
    if (isPlanRestrictedRoute(pathname)) {
      const planValidation = await validatePlanAccess(user.id, pathname, supabase);

      if (!planValidation.valid) {
        if (SECURITY_CONFIG.enableAccessLogging) {
          console.log(`❌ [MIDDLEWARE] Plan validation failed: ${planValidation.reason}`);
        }

        return redirectToUnauthorized(request, planValidation);
      }
    }

    // Log de acceso exitoso
    const processingTime = Date.now() - startTime;
    if (SECURITY_CONFIG.enableAccessLogging) {
      console.log(`✅ [MIDDLEWARE] Access granted to ${pathname} in ${processingTime}ms`);
    }

    // Agregar headers de seguridad y retornar
    return addSecurityHeaders(supabaseResponse);

  } catch (error) {
    console.error('❌ [MIDDLEWARE] Critical error:', error);

    // En caso de error crítico, denegar acceso por seguridad
    return redirectToLogin(request);
  }
}

// Funciones auxiliares para validación de rutas
function isPublicRoute(pathname: string): boolean {
  return ROUTE_PERMISSIONS.public.some(path =>
    pathname === path || pathname.startsWith(path + '/')
  );
}

function isAuthenticatedRoute(pathname: string): boolean {
  return ROUTE_PERMISSIONS.authenticated.some(path =>
    pathname.startsWith(path)
  );
}

function isPlanRestrictedRoute(pathname: string): boolean {
  return Object.keys(ROUTE_PERMISSIONS.planRestricted).some(path =>
    pathname.startsWith(path)
  );
}

// Funciones de validación
async function checkIfLegitimateUser(user: any, supabase: any): Promise<{
  isLegitimate: boolean;
  accountType: 'free' | 'paid' | 'unknown';
  reason?: string;
}> {
  try {
    // Verificar si el usuario tiene metadata que indique registro legítimo
    const userMetadata = user.user_metadata || {};
    const appMetadata = user.app_metadata || {};

    // Verificar si se registró para cuenta gratuita
    if (userMetadata.free_account || userMetadata.created_via === 'free_registration') {
      return { isLegitimate: true, accountType: 'free', reason: 'Free account registration' };
    }

    // Verificar si hay transacciones de Stripe asociadas
    const { data: transactions, error } = await supabase
      .from('stripe_transactions')
      .select('id, status, plan_id')
      .eq('user_id', user.id)
      .limit(1);

    if (!error && transactions && transactions.length > 0) {
      return { isLegitimate: true, accountType: 'paid', reason: 'Has payment transactions' };
    }

    // Verificar si el email está en la lista de usuarios invitados/creados por admin
    if (appMetadata.created_by_admin || userMetadata.invited_by_admin) {
      return { isLegitimate: true, accountType: 'free', reason: 'Admin created account' };
    }

    // Si llegamos aquí, es sospechoso
    return {
      isLegitimate: false,
      accountType: 'unknown',
      reason: 'No legitimate registration method found'
    };

  } catch (error) {
    console.error('Error checking user legitimacy:', error);
    return { isLegitimate: false, accountType: 'unknown', reason: 'Verification error' };
  }
}

async function createRecoveryProfile(
  userId: string,
  email: string | undefined,
  accountType: 'free' | 'paid',
  supabase: any
): Promise<void> {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    let profileData;

    if (accountType === 'free') {
      // Crear perfil gratuito con expiración
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 5); // 5 días

      profileData = {
        user_id: userId,
        subscription_plan: 'free',
        monthly_token_limit: 50000,
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: true, // Las cuentas gratuitas se consideran verificadas
        plan_expires_at: expirationDate.toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        security_flags: {
          recovered_profile: true,
          recovery_date: new Date().toISOString(),
          original_account_type: 'free'
        }
      };
    } else {
      // Para cuentas de pago, crear perfil básico sin verificación
      profileData = {
        user_id: userId,
        subscription_plan: 'free', // Temporal hasta que se verifique el pago
        monthly_token_limit: 50000,
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: false, // Requiere verificación manual
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        security_flags: {
          recovered_profile: true,
          recovery_date: new Date().toISOString(),
          original_account_type: 'paid',
          requires_manual_verification: true
        }
      };
    }

    const { error } = await supabase
      .from('user_profiles')
      .insert([profileData]);

    if (error) {
      console.error('Error creating recovery profile:', error);
      throw error;
    }
  } catch (error) {
    console.error('Failed to create recovery profile:', error);
    throw error;
  }
}

async function validateUserProfile(userId: string, supabase: any): Promise<{
  valid: boolean;
  reason?: string;
}> {
  try {
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified, plan_expires_at, auto_renew, security_flags')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return { valid: false, reason: 'Profile not found' };
    }

    // Verificar expiración para todos los planes que tengan plan_expires_at
    if (profile.plan_expires_at) {
      const now = new Date();
      const expiresAt = new Date(profile.plan_expires_at);

      if (now > expiresAt) {
        // Determinar el motivo de expiración basado en el plan
        let reason = 'Account expired';
        if (profile.subscription_plan === 'free') {
          reason = 'Free account expired';
        } else {
          reason = 'Subscription grace period expired';
        }

        return { valid: false, reason };
      }
    }

    // Verificar pago para planes de pago
    if (SECURITY_CONFIG.requirePaymentVerification &&
        profile.subscription_plan !== 'free' &&
        !profile.payment_verified) {
      return { valid: false, reason: 'Payment not verified' };
    }

    return { valid: true };
  } catch (error) {
    console.error('Error validating user profile:', error);
    return { valid: false, reason: 'Validation error' };
  }
}

async function validatePlanAccess(userId: string, pathname: string, supabase: any): Promise<{
  valid: boolean;
  reason?: string;
  requiredPlans?: string[];
}> {
  try {
    // Encontrar qué planes se requieren para esta ruta
    const requiredPlans = Object.entries(ROUTE_PERMISSIONS.planRestricted)
      .find(([path]) => pathname.startsWith(path))?.[1];

    if (!requiredPlans) {
      return { valid: true };
    }

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return {
        valid: false,
        reason: 'Profile not found',
        requiredPlans
      };
    }

    // Verificar si el plan del usuario está en la lista de planes permitidos
    if (!requiredPlans.includes(profile.subscription_plan)) {
      return {
        valid: false,
        reason: `Plan ${profile.subscription_plan} not sufficient`,
        requiredPlans
      };
    }

    return { valid: true };
  } catch (error) {
    console.error('Error validating plan access:', error);
    return {
      valid: false,
      reason: 'Validation error',
      requiredPlans: []
    };
  }
}

// Funciones de redirección
function redirectToLogin(request: NextRequest): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/login';
  url.searchParams.set('redirect', request.nextUrl.pathname);
  return NextResponse.redirect(url);
}

function redirectToPayment(request: NextRequest, reason?: string): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/payment';
  if (reason) {
    url.searchParams.set('reason', reason);
  }
  return NextResponse.redirect(url);
}

function redirectToUnauthorized(request: NextRequest, validation: any): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/auth/unauthorized';
  url.searchParams.set('reason', validation.reason || 'Access denied');
  url.searchParams.set('feature', request.nextUrl.pathname);
  if (validation.requiredPlans) {
    url.searchParams.set('required_plan', validation.requiredPlans.join(','));
  }
  return NextResponse.redirect(url);
}

// Función para agregar headers de seguridad
function addSecurityHeaders(response: NextResponse): NextResponse {
  // Headers de seguridad
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // CSP básico
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://d3js.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.stripe.com;"
  );

  return response;
}

// Configuración del matcher para definir en qué rutas se ejecutará el middleware.
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     *
     * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,
     * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.
     * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.
     *
     * La expresión regular abajo intenta cubrir los casos más comunes:
     */
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)',
    // Explicación de la regex mejorada:
    // /((?!             // Inicio de grupo de no coincidencia (negative lookahead)
    // _next/static      // No coincidir con _next/static
    // |_next/image      // O no coincidir con _next/image
    // |favicon.ico     // O no coincidir con favicon.ico
    // |manifest.json   // O no coincidir con manifest.json (común para PWAs)
    // |robots.txt      // O no coincidir con robots.txt
    // |.*\\..*         // O no coincidir con cualquier cosa que contenga un punto (archivos como .png, .css, etc.)
    // ).*)             // Fin del lookahead, coincide con cualquier otra cosa
  ],
};