// src/lib/services/emailNotificationService.ts
// Servicio para enviar notificaciones por email

import { supabaseAdmin } from '@/lib/supabase/admin';

export interface EmailNotification {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  type: 'subscription_cancelled' | 'grace_period_ending' | 'plan_expired' | 'payment_failed' | 'welcome' | 'other';
  userId?: string;
  metadata?: Record<string, any>;
}

export class EmailNotificationService {
  
  /**
   * Enviar notificación de cancelación de suscripción
   */
  static async sendSubscriptionCancelledNotification(
    userEmail: string,
    userName: string,
    planName: string,
    gracePeriodEnd: string,
    userId?: string
  ): Promise<boolean> {
    try {
      const gracePeriodDate = new Date(gracePeriodEnd);
      const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

      const daysRemaining = Math.ceil(
        (gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Suscripción Cancelada - OposI</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2563eb;">Suscripción Cancelada</h1>
            
            <p>Hola ${userName},</p>
            
            <p>Hemos recibido tu solicitud de cancelación de la suscripción al <strong>Plan ${planName}</strong>.</p>
            
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #059669;">📅 Período de Gracia Activo</h3>
              <p><strong>Mantienes acceso completo hasta:</strong> ${formattedDate}</p>
              <p><strong>Días restantes:</strong> ${daysRemaining} días</p>
              <p>Durante este período, puedes seguir usando todas las funciones de tu plan actual.</p>
            </div>
            
            <h3>¿Qué sucede después?</h3>
            <ul>
              <li>Tu acceso a las funciones premium finalizará el ${formattedDate}</li>
              <li>Tu cuenta se convertirá automáticamente al Plan Gratuito</li>
              <li>Conservarás acceso a las funciones básicas de OposI</li>
              <li>Tus documentos y progreso se mantendrán guardados</li>
            </ul>
            
            <h3>¿Cambiaste de opinión?</h3>
            <p>Si deseas reactivar tu suscripción, puedes hacerlo en cualquier momento desde tu panel de control:</p>
            <p style="text-align: center; margin: 20px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan" 
                 style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Reactivar Suscripción
              </a>
            </p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
            
            <p style="font-size: 14px; color: #6b7280;">
              Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>
              Equipo de OposI
            </p>
          </div>
        </body>
        </html>
      `;

      const textContent = `
Suscripción Cancelada - OposI

Hola ${userName},

Hemos recibido tu solicitud de cancelación de la suscripción al Plan ${planName}.

PERÍODO DE GRACIA ACTIVO:
- Mantienes acceso completo hasta: ${formattedDate}
- Días restantes: ${daysRemaining} días

¿Qué sucede después?
- Tu acceso a las funciones premium finalizará el ${formattedDate}
- Tu cuenta se convertirá automáticamente al Plan Gratuito
- Conservarás acceso a las funciones básicas de OposI
- Tus documentos y progreso se mantendrán guardados

¿Cambiaste de opinión?
Puedes reactivar tu suscripción en cualquier momento desde: ${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
      `;

      return await this.sendEmail({
        to: userEmail,
        subject: `Suscripción cancelada - Acceso hasta el ${gracePeriodDate.toLocaleDateString('es-ES')}`,
        htmlContent,
        textContent,
        type: 'subscription_cancelled',
        userId,
        metadata: {
          planName,
          gracePeriodEnd,
          daysRemaining,
          userName
        }
      });

    } catch (error) {
      console.error('Error enviando notificación de cancelación:', error);
      return false;
    }
  }

  /**
   * Enviar recordatorio de que el período de gracia está por terminar
   */
  static async sendGracePeriodEndingNotification(
    userEmail: string,
    userName: string,
    planName: string,
    gracePeriodEnd: string,
    userId?: string
  ): Promise<boolean> {
    try {
      const gracePeriodDate = new Date(gracePeriodEnd);
      const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const hoursRemaining = Math.ceil(
        (gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60)
      );

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Tu acceso premium termina pronto - OposI</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #dc2626;">⏰ Tu acceso premium termina pronto</h1>
            
            <p>Hola ${userName},</p>
            
            <p>Te recordamos que tu acceso al <strong>Plan ${planName}</strong> terminará el <strong>${formattedDate}</strong> (en aproximadamente ${hoursRemaining} horas).</p>
            
            <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
              <h3 style="margin-top: 0; color: #92400e;">¿Quieres continuar con tu plan premium?</h3>
              <p>Reactivar tu suscripción es fácil y rápido. Mantén acceso a todas las funciones avanzadas de OposI.</p>
            </div>
            
            <p style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan" 
                 style="background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                Reactivar Mi Suscripción
              </a>
            </p>
            
            <p style="font-size: 14px; color: #6b7280;">
              Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.
            </p>
          </div>
        </body>
        </html>
      `;

      const textContent = `
Tu acceso premium termina pronto - OposI

Hola ${userName},

Te recordamos que tu acceso al Plan ${planName} terminará el ${formattedDate} (en aproximadamente ${hoursRemaining} horas).

¿Quieres continuar con tu plan premium?
Reactivar tu suscripción: ${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan

Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.

Equipo de OposI
      `;

      return await this.sendEmail({
        to: userEmail,
        subject: `⏰ Tu Plan ${planName} termina en ${hoursRemaining} horas`,
        htmlContent,
        textContent,
        type: 'grace_period_ending',
        userId,
        metadata: {
          planName,
          gracePeriodEnd,
          hoursRemaining,
          userName
        }
      });

    } catch (error) {
      console.error('Error enviando recordatorio de período de gracia:', error);
      return false;
    }
  }

  /**
   * Función base para enviar emails
   * NOTA: Implementar con tu proveedor de email preferido (SendGrid, Resend, etc.)
   */
  private static async sendEmail(notification: EmailNotification): Promise<boolean> {
    try {
      // TODO: Implementar con tu proveedor de email
      // Ejemplo con fetch a un endpoint de email:
      
      console.log('📧 Enviando email:', {
        to: notification.to,
        subject: notification.subject,
        type: notification.type
      });

      // Guardar notificación en base de datos para tracking
      await this.logEmailNotification(notification);

      // Por ahora, solo loggeamos (implementar con proveedor real)
      console.log('✅ Email enviado exitosamente');
      return true;

    } catch (error) {
      console.error('❌ Error enviando email:', error);
      return false;
    }
  }

  /**
   * Registrar notificación en base de datos para tracking
   */
  private static async logEmailNotification(notification: EmailNotification): Promise<void> {
    try {
      const insertData: any = {
        recipient_email: notification.to,
        subject: notification.subject,
        type: notification.type,
        sent_at: new Date().toISOString(),
        status: 'sent'
      };

      // Agregar user_id si está disponible
      if (notification.userId) {
        insertData.user_id = notification.userId;
      }

      // Agregar metadata si está disponible
      if (notification.metadata) {
        insertData.metadata = notification.metadata;
      }

      await supabaseAdmin
        .from('email_notifications')
        .insert(insertData);

      console.log('📝 Email notification logged:', {
        type: notification.type,
        recipient: notification.to,
        userId: notification.userId || 'N/A'
      });
    } catch (error) {
      console.error('Error logging email notification:', error);
      // No lanzar error, es solo para tracking
    }
  }

  /**
   * Obtener historial de notificaciones de un usuario
   */
  static async getUserNotifications(
    userId: string,
    limit: number = 50,
    type?: string
  ): Promise<{
    notifications: any[];
    total: number;
  }> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('sent_at', { ascending: false });

      if (type) {
        query = query.eq('type', type);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      // Obtener total de notificaciones
      let countQuery = supabaseAdmin
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (type) {
        countQuery = countQuery.eq('type', type);
      }

      const { count } = await countQuery;

      return {
        notifications: notifications || [],
        total: count || 0
      };

    } catch (error) {
      console.error('Error obteniendo notificaciones del usuario:', error);
      return {
        notifications: [],
        total: 0
      };
    }
  }

  /**
   * Obtener estadísticas de notificaciones por tipo
   */
  static async getNotificationStats(
    startDate?: string,
    endDate?: string
  ): Promise<{
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    total: number;
    recentNotifications: any[];
  }> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*');

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      const byType = (notifications || []).reduce((acc, notif) => {
        acc[notif.type] = (acc[notif.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const byStatus = (notifications || []).reduce((acc, notif) => {
        acc[notif.status] = (acc[notif.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const recentNotifications = (notifications || [])
        .sort((a, b) => new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime())
        .slice(0, 10);

      return {
        byType,
        byStatus,
        total: notifications?.length || 0,
        recentNotifications
      };

    } catch (error) {
      console.error('Error obteniendo estadísticas de notificaciones:', error);
      return {
        byType: {},
        byStatus: {},
        total: 0,
        recentNotifications: []
      };
    }
  }
}
