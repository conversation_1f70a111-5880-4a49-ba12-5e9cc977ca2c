import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function GET(req: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Obtener usuario autenticado
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (!user || authError) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener perfil del usuario
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('subscription_plan')
      .eq('user_id', user.id)
      .single();

    console.log('🔍 [/api/user/plan] User ID:', user.id);
    console.log('🔍 [/api/user/plan] Profile data:', profile);
    console.log('🔍 [/api/user/plan] Profile error:', profileError);

    if (profileError) {
      console.error('Error obteniendo perfil:', profileError);
      // Si no existe perfil, asumir plan gratuito
      console.log('❌ [/api/user/plan] Returning free plan due to error');
      return NextResponse.json({ plan: 'free' });
    }

    const finalPlan = profile?.subscription_plan || 'free';
    console.log('✅ [/api/user/plan] Returning plan:', finalPlan);

    return NextResponse.json({
      plan: finalPlan
    });

  } catch (error) {
    console.error('Error en API user/plan:', error);
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
