// src/lib/utils/planLimits.ts
// Configuración centralizada de límites y características por plan

export interface PlanLimits {
  documents: number; // -1 = ilimitado
  mindMapsPerWeek?: number;      // Para planes de pago con límites semanales
  testsPerWeek?: number;         // Para planes de pago con límites semanales
  flashcardsPerWeek?: number;    // Para planes de pago con límites semanales
  monthlyTokens?: number;        // Para planes de pago con límites mensuales
  mindMapsForTrial?: number;     // Específico para trial de 5 días
  testsForTrial?: number;        // Específico para trial de 5 días
  flashcardsForTrial?: number;   // Específico para trial de 5 días
  tokensForTrial?: number;       // Específico para trial de 5 días
  features: string[];
}

export interface PlanConfiguration {
  id: string;
  name: string;
  price: number; // en centavos
  limits: PlanLimits;
  features: string[];
  restrictedFeatures: string[];
}

// Configuración completa de planes con límites y características
export const PLAN_CONFIGURATIONS: Record<string, PlanConfiguration> = {
  free: {
    id: 'free',
    name: 'Plan Gratis',
    price: 0,
    limits: {
      documents: 1,
      mindMapsForTrial: 2,        // Límite total para el trial de 5 días
      testsForTrial: 10,          // Límite total para el trial de 5 días
      flashcardsForTrial: 10,     // Límite total para el trial de 5 días
      tokensForTrial: 50000,      // Límite total para el trial de 5 días
      features: ['document_upload', 'test_generation', 'flashcard_generation', 'mind_map_generation']
    },
    features: [
      'document_upload',
      'test_generation',
      'flashcard_generation', 
      'mind_map_generation'
    ],
    restrictedFeatures: [
      'study_planning',
      'ai_tutor_chat',
      'summary_a1_a2'
    ]
  },
  usuario: {
    id: 'usuario',
    name: 'Plan Usuario',
    price: 1000, // €10.00
    limits: {
      documents: -1, // ilimitado
      mindMapsPerWeek: -1,        // Ilimitado semanal
      testsPerWeek: -1,           // Ilimitado semanal
      flashcardsPerWeek: -1,      // Ilimitado semanal
      monthlyTokens: 1000000,     // Límite mensual
      features: ['document_upload', 'ai_tutor_chat', 'test_generation', 'flashcard_generation', 'mind_map_generation']
    },
    features: [
      'document_upload',
      'ai_tutor_chat',
      'test_generation',
      'flashcard_generation',
      'mind_map_generation'
    ],
    restrictedFeatures: [
      'study_planning',
      'summary_a1_a2'
    ]
  },
  pro: {
    id: 'pro',
    name: 'Plan Pro',
    price: 1500, // €15.00
    limits: {
      documents: -1, // ilimitado
      mindMapsPerWeek: -1,        // Ilimitado semanal
      testsPerWeek: -1,           // Ilimitado semanal
      flashcardsPerWeek: -1,      // Ilimitado semanal
      monthlyTokens: 1000000,     // Límite mensual
      features: ['document_upload', 'study_planning', 'ai_tutor_chat', 'test_generation', 'flashcard_generation', 'mind_map_generation', 'summary_a1_a2']
    },
    features: [
      'document_upload',
      'study_planning',
      'ai_tutor_chat',
      'test_generation',
      'flashcard_generation',
      'mind_map_generation',
      'summary_a1_a2'
    ],
    restrictedFeatures: []
  }
};

// Funciones de utilidad
export function getPlanConfiguration(planId: string): PlanConfiguration | null {
  return PLAN_CONFIGURATIONS[planId] || null;
}

export function getTokenLimitForPlan(planId: string): number {
  const config = getPlanConfiguration(planId);
  if (!config) return 50000;

  // Para plan gratuito, usar tokensForTrial
  if (planId === 'free') {
    return config.limits.tokensForTrial || 50000;
  }

  // Para planes de pago, usar monthlyTokens
  return config.limits.monthlyTokens || 1000000;
}

export function hasFeatureAccess(planId: string, feature: string): boolean {
  const config = getPlanConfiguration(planId);
  if (!config) return false;

  // Si la característica está en la lista de restringidas, no tiene acceso
  if (config.restrictedFeatures.includes(feature)) {
    return false;
  }

  // Si no está restringida, verificar si está en las características permitidas
  return config.features.includes(feature);
}

export function getWeeklyLimit(planId: string, limitType: 'mindMaps' | 'tests' | 'flashcards'): number {
  const config = getPlanConfiguration(planId);
  if (!config) return 0;

  switch (limitType) {
    case 'mindMaps':
      return config.limits.mindMapsPerWeek || 0;
    case 'tests':
      return config.limits.testsPerWeek || 0;
    case 'flashcards':
      return config.limits.flashcardsPerWeek || 0;
    default:
      return 0;
  }
}

// Nueva función para obtener límites de trial (para plan gratuito)
export function getTrialLimit(planId: string, limitType: 'mindMaps' | 'tests' | 'flashcards' | 'tokens'): number {
  const config = getPlanConfiguration(planId);
  if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica

  switch (limitType) {
    case 'mindMaps':
      return config.limits.mindMapsForTrial || 0;
    case 'tests':
      return config.limits.testsForTrial || 0;
    case 'flashcards':
      return config.limits.flashcardsForTrial || 0;
    case 'tokens':
      return config.limits.tokensForTrial || 0;
    default:
      return 0;
  }
}

export function isUnlimited(limit: number): boolean {
  return limit === -1;
}

// Validar si un usuario puede realizar una acción específica
export function canPerformAction(
  planId: string,
  feature: string,
  currentUsage: number,
  limitType?: 'mindMaps' | 'tests' | 'flashcards'
): { allowed: boolean; reason?: string } {
  const config = getPlanConfiguration(planId);

  if (!config) {
    return { allowed: false, reason: 'Plan no válido' };
  }

  // Verificar si tiene acceso a la característica
  if (!hasFeatureAccess(planId, feature)) {
    return { allowed: false, reason: `Característica ${feature} no disponible en ${config.name}` };
  }

  // Verificar límites semanales si aplica
  if (limitType) {
    const weeklyLimit = getWeeklyLimit(planId, limitType);
    if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {
      return { allowed: false, reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})` };
    }
  }

  return { allowed: true };
}

// Función para verificar acceso de usuario (para uso en frontend)
export async function checkUserFeatureAccess(feature: string): Promise<boolean> {
  try {
    // Obtener el plan del usuario desde la API
    const response = await fetch('/api/user/plan');
    if (!response.ok) {
      console.error('Error obteniendo plan del usuario');
      // Si hay error, asumir plan gratuito
      return hasFeatureAccess('free', feature);
    }

    const { plan } = await response.json();
    const userPlan = plan || 'free';

    // Usar la misma lógica que la función hasFeatureAccess
    return hasFeatureAccess(userPlan, feature);
  } catch (error) {
    console.error('Error verificando acceso a característica:', error);
    // En caso de error, asumir plan gratuito
    return hasFeatureAccess('free', feature);
  }
}
