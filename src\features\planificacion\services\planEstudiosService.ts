import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/features/auth/services/authService';
import { PlanEstudios, ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';
import { PlanEstudiosEstructurado } from './planGeneratorService';
import { User } from '@supabase/supabase-js';

/**
 * Guarda un plan de estudios generado en la base de datos (versión para cliente)
 */
export async function guardarPlanEstudios(
  temarioId: string,
  planData: PlanEstudiosEstructurado,
  titulo?: string
): Promise<string | null> {
  try {
    // Usar cliente del navegador
    const { user: clientUser, error: authError } = await obtenerUsuarioActual();
    if (!clientUser || authError) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('planes_estudios')
      .insert([{
        user_id: clientUser.id,
        temario_id: temarioId,
        titulo: titulo || 'Plan de Estudios',
        plan_data: planData,
        activo: true, // El trigger se encargará de desactivar otros planes
        version: 1
      }])
      .select()
      .single();

    if (error) {
      console.error('Error al guardar plan de estudios:', error);
      return null;
    }

    console.log('✅ Plan de estudios guardado exitosamente:', data.id);
    return data.id;
  } catch (error) {
    console.error('Error al guardar plan de estudios:', error);
    return null;
  }
}

/**
 * Guarda un plan de estudios generado en la base de datos (versión para servidor)
 */
export async function guardarPlanEstudiosServidor(
  temarioId: string,
  planData: PlanEstudiosEstructurado,
  user: User,
  titulo?: string
): Promise<string | null> {
  try {
    const { createServerSupabaseClient } = await import('@/lib/supabase/server');
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('planes_estudios')
      .insert([{
        user_id: user.id,
        temario_id: temarioId,
        titulo: titulo || 'Plan de Estudios',
        plan_data: planData,
        activo: true, // El trigger se encargará de desactivar otros planes
        version: 1
      }])
      .select()
      .single();

    if (error) {
      console.error('Error al guardar plan de estudios (servidor):', error);
      return null;
    }

    console.log('✅ Plan de estudios guardado exitosamente (servidor):', data.id);
    return data.id;
  } catch (error) {
    console.error('Error al guardar plan de estudios (servidor):', error);
    return null;
  }
}

/**
 * Obtiene todos los planes de estudios de un temario (historial)
 */
export async function obtenerHistorialPlanes(temarioId: string): Promise<PlanEstudios[]> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return [];
    }

    const { data, error } = await supabase
      .from('planes_estudios')
      .select('*')
      .eq('user_id', user.id)
      .eq('temario_id', temarioId)
      .order('fecha_generacion', { ascending: false });

    if (error) {
      console.error('Error al obtener historial de planes:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener historial de planes:', error);
    return [];
  }
}

/**
 * Actualiza las notas de un plan de estudios
 */
export async function actualizarNotasPlan(planId: string, notas: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('planes_estudios')
      .update({ 
        notas,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', planId);

    if (error) {
      console.error('Error al actualizar notas del plan:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al actualizar notas del plan:', error);
    return false;
  }
}

/**
 * Marca un plan como activo y desactiva los demás
 */
export async function activarPlan(planId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('planes_estudios')
      .update({ 
        activo: true,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', planId);

    if (error) {
      console.error('Error al activar plan:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al activar plan:', error);
    return false;
  }
}

/**
 * Elimina un plan de estudios
 */
export async function eliminarPlan(planId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('planes_estudios')
      .delete()
      .eq('id', planId);

    if (error) {
      console.error('Error al eliminar plan:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al eliminar plan:', error);
    return false;
  }
}

/**
 * Guarda el progreso de una tarea del plan
 */
export async function guardarProgresoTarea(
  planId: string,
  semanaNúmero: number,
  diaNombre: string,
  tareaTitulo: string,
  tareaTipo: 'estudio' | 'repaso' | 'practica' | 'evaluacion',
  completado: boolean,
  tiempoRealMinutos?: number,
  notasProgreso?: string,
  calificacion?: number
): Promise<boolean> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return false;
    }

    // Verificar si ya existe un registro de progreso para esta tarea
    const { data: existente } = await supabase
      .from('progreso_plan_estudios')
      .select('id')
      .eq('plan_id', planId)
      .eq('user_id', user.id)
      .eq('semana_numero', semanaNúmero)
      .eq('dia_nombre', diaNombre)
      .eq('tarea_titulo', tareaTitulo)
      .single();

    if (existente) {
      // Actualizar registro existente
      const { error } = await supabase
        .from('progreso_plan_estudios')
        .update({
          completado,
          fecha_completado: completado ? new Date().toISOString() : null,
          tiempo_real_minutos: tiempoRealMinutos,
          notas_progreso: notasProgreso,
          calificacion,
          actualizado_en: new Date().toISOString()
        })
        .eq('id', existente.id);

      if (error) {
        console.error('Error al actualizar progreso:', error);
        return false;
      }
    } else {
      // Crear nuevo registro
      const { error } = await supabase
        .from('progreso_plan_estudios')
        .insert([{
          plan_id: planId,
          user_id: user.id,
          semana_numero: semanaNúmero,
          dia_nombre: diaNombre,
          tarea_titulo: tareaTitulo,
          tarea_tipo: tareaTipo,
          completado,
          fecha_completado: completado ? new Date().toISOString() : null,
          tiempo_real_minutos: tiempoRealMinutos,
          notas_progreso: notasProgreso,
          calificacion
        }]);

      if (error) {
        console.error('Error al crear progreso:', error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error al guardar progreso de tarea:', error);
    return false;
  }
}

/**
 * Obtiene el progreso de un plan de estudios
 */
export async function obtenerProgresoPlan(planId: string): Promise<ProgresoPlanEstudios[]> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return [];
    }

    const { data, error } = await supabase
      .from('progreso_plan_estudios')
      .select('*')
      .eq('plan_id', planId)
      .eq('user_id', user.id)
      .order('semana_numero', { ascending: true })
      .order('creado_en', { ascending: true });

    if (error) {
      console.error('Error al obtener progreso del plan:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener progreso del plan:', error);
    return [];
  }
}

/**
 * Obtiene estadísticas del progreso del plan
 */
export async function obtenerEstadisticasProgreso(planId: string): Promise<{
  totalTareas: number;
  tareasCompletadas: number;
  porcentajeCompletado: number;
  tiempoTotalEstimado: number;
  tiempoTotalReal: number;
  semanasCompletadas: number;
  totalSemanas: number;
}> {
  try {
    const progreso = await obtenerProgresoPlan(planId);
    const plan = await supabase
      .from('planes_estudios')
      .select('plan_data')
      .eq('id', planId)
      .single();

    if (!plan.data) {
      return {
        totalTareas: 0,
        tareasCompletadas: 0,
        porcentajeCompletado: 0,
        tiempoTotalEstimado: 0,
        tiempoTotalReal: 0,
        semanasCompletadas: 0,
        totalSemanas: 0
      };
    }

    const planData = plan.data.plan_data as PlanEstudiosEstructurado;
    const totalSemanas = planData.semanas.length;
    
    // Calcular total de tareas
    let totalTareas = 0;
    planData.semanas.forEach(semana => {
      semana.dias.forEach(dia => {
        totalTareas += dia.tareas.length;
      });
    });

    const tareasCompletadas = progreso.filter(p => p.completado).length;
    const porcentajeCompletado = totalTareas > 0 ? (tareasCompletadas / totalTareas) * 100 : 0;
    
    const tiempoTotalReal = progreso
      .filter(p => p.tiempo_real_minutos)
      .reduce((total, p) => total + (p.tiempo_real_minutos || 0), 0);

    // Calcular semanas completadas (todas las tareas de la semana completadas)
    let semanasCompletadas = 0;
    planData.semanas.forEach(semana => {
      const tareasSemanaTotales = semana.dias.reduce((total, dia) => total + dia.tareas.length, 0);
      const tareasSemanCompletadas = progreso.filter(p => 
        p.semana_numero === semana.numero && p.completado
      ).length;
      
      if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {
        semanasCompletadas++;
      }
    });

    return {
      totalTareas,
      tareasCompletadas,
      porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,
      tiempoTotalEstimado: 0, // Se puede calcular desde el plan
      tiempoTotalReal,
      semanasCompletadas,
      totalSemanas
    };
  } catch (error) {
    console.error('Error al obtener estadísticas de progreso:', error);
    return {
      totalTareas: 0,
      tareasCompletadas: 0,
      porcentajeCompletado: 0,
      tiempoTotalEstimado: 0,
      tiempoTotalReal: 0,
      semanasCompletadas: 0,
      totalSemanas: 0
    };
  }
}
