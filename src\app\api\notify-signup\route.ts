// src/app/api/notify-signup/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const { email, customerName, planName } = await request.json();

    console.log('Using Resend API key starting with:', process.env.RESEND_API_KEY?.substring(0, 10) + '...');
    console.log('Sending notification to:', process.env.NOTIFICATION_EMAIL);

    if (!email || !planName) {
      return NextResponse.json(
        { error: 'Email y nombre del plan son requeridos' },
        { status: 400 }
      );
    }

    const emailData = {
      from: 'OposiAI Notificaciones <<EMAIL>>',
      to: [process.env.NOTIFICATION_EMAIL!],
      subject: `🚀 Nueva Solicitud de Suscripción OposiAI: Plan ${planName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Nueva Solicitud de Suscripción</h2>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #1e40af;">Detalles del Cliente</h3>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Nombre:</strong> ${customerName || 'No proporcionado'}</p>
            <p><strong>Plan Seleccionado:</strong> ${planName}</p>
            <p><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</p>
          </div>

          <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <h4 style="margin-top: 0; color: #92400e;">Acción Requerida</h4>
            <p style="margin-bottom: 0;">Por favor, añade manualmente este usuario a Supabase con el plan correspondiente.</p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px;">
              Este email fue generado automáticamente por el sistema de suscripciones de OposiAI.
            </p>
          </div>
        </div>
      `,
    };

    console.log('Attempting to send email with Resend...');
    console.log('Email payload:', {
      from: emailData.from,
      to: emailData.to,
      subject: emailData.subject
    });

    const data = await resend.emails.send(emailData);

    console.log('Email enviado exitosamente:', data);

    return NextResponse.json({
      success: true,
      message: 'Notificación enviada correctamente',
      emailData: data
    });

  } catch (error) {
    console.error('Error enviando email con Resend:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al enviar la notificación',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
