// src/lib/services/emailNotificationService.ts
// Servicio para enviar notificaciones por email

import { supabaseAdmin } from '@/lib/supabase/admin';

export interface EmailNotification {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  type: 'subscription_cancelled' | 'grace_period_ending' | 'plan_expired' | 'payment_failed' | 'welcome' | 'other';
  userId?: string;
  metadata?: Record<string, any>;
}

export class EmailNotificationService {
  
  /**
   * Enviar notificación de cancelación de suscripción
   */
  static async sendSubscriptionCancelledNotification(
    userEmail: string,
    userName: string,
    planName: string,
    gracePeriodEnd: string,
    userId?: string
  ): Promise<boolean> {
    try {
      const gracePeriodDate = new Date(gracePeriodEnd);
      const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

      const daysRemaining = Math.ceil(
        (gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Suscripción Cancelada - OposI</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2563eb;">Suscripción Cancelada</h1>
            
            <p>Hola ${userName},</p>
            
            <p>Hemos recibido tu solicitud de cancelación de la suscripción al <strong>Plan ${planName}</strong>.</p>
            
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #059669;">📅 Período de Gracia Activo</h3>
              <p><strong>Mantienes acceso completo hasta:</strong> ${formattedDate}</p>
              <p><strong>Días restantes:</strong> ${daysRemaining} días</p>
              <p>Durante este período, puedes seguir usando todas las funciones de tu plan actual.</p>
            </div>
            
            <h3>¿Qué sucede después?</h3>
            <ul>
              <li>Tu acceso a las funciones premium finalizará el ${formattedDate}</li>
              <li>Tu cuenta se convertirá automáticamente al Plan Gratuito</li>
              <li>Conservarás acceso a las funciones básicas de OposI</li>
              <li>Tus documentos y progreso se mantendrán guardados</li>
            </ul>
            
            <h3>¿Cambiaste de opinión?</h3>
            <p>Si deseas reactivar tu suscripción, puedes hacerlo en cualquier momento desde tu panel de control:</p>
            <p style="text-align: center; margin: 20px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan" 
                 style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Reactivar Suscripción
              </a>
            </p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
            
            <p style="font-size: 14px; color: #6b7280;">
              Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>
              Equipo de OposI
            </p>
          </div>
        </body>
        </html>
      `;

      const textContent = `
Suscripción Cancelada - OposI

Hola ${userName},

Hemos recibido tu solicitud de cancelación de la suscripción al Plan ${planName}.

PERÍODO DE GRACIA ACTIVO:
- Mantienes acceso completo hasta: ${formattedDate}
- Días restantes: ${daysRemaining} días

¿Qué sucede después?
- Tu acceso a las funciones premium finalizará el ${formattedDate}
- Tu cuenta se convertirá automáticamente al Plan Gratuito
- Conservarás acceso a las funciones básicas de OposI
- Tus documentos y progreso se mantendrán guardados

¿Cambiaste de opinión?
Puedes reactivar tu suscripción en cualquier momento desde: ${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
      `;

      return await this.sendEmail({
        to: userEmail,
        subject: `Suscripción cancelada - Acceso hasta el ${gracePeriodDate.toLocaleDateString('es-ES')}`,
        htmlContent,
        textContent,
        type: 'subscription_cancelled',
        userId,
        metadata: {
          planName,
          gracePeriodEnd,
          daysRemaining,
          userName
        }
      });

    } catch (error) {
      console.error('Error enviando notificación de cancelación:', error);
      return false;
    }
  }

  /**
   * Enviar recordatorio de que el período de gracia está por terminar
   */
  static async sendGracePeriodEndingNotification(
    userEmail: string,
    userName: string,
    planName: string,
    gracePeriodEnd: string,
    userId?: string
  ): Promise<boolean> {
    try {
      const gracePeriodDate = new Date(gracePeriodEnd);
      const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const hoursRemaining = Math.ceil(
        (gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60)
      );

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Tu acceso premium termina pronto - OposI</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #dc2626;">⏰ Tu acceso premium termina pronto</h1>
            
            <p>Hola ${userName},</p>
            
            <p>Te recordamos que tu acceso al <strong>Plan ${planName}</strong> terminará el <strong>${formattedDate}</strong> (en aproximadamente ${hoursRemaining} horas).</p>
            
            <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
              <h3 style="margin-top: 0; color: #92400e;">¿Quieres continuar con tu plan premium?</h3>
              <p>Reactivar tu suscripción es fácil y rápido. Mantén acceso a todas las funciones avanzadas de OposI.</p>
            </div>
            
            <p style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan" 
                 style="background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                Reactivar Mi Suscripción
              </a>
            </p>
            
            <p style="font-size: 14px; color: #6b7280;">
              Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.
            </p>
          </div>
        </body>
        </html>
      `;

      const textContent = `
Tu acceso premium termina pronto - OposI

Hola ${userName},

Te recordamos que tu acceso al Plan ${planName} terminará el ${formattedDate} (en aproximadamente ${hoursRemaining} horas).

¿Quieres continuar con tu plan premium?
Reactivar tu suscripción: ${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan

Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.

Equipo de OposI
      `;

      return await this.sendEmail({
        to: userEmail,
        subject: `⏰ Tu Plan ${planName} termina en ${hoursRemaining} horas`,
        htmlContent,
        textContent,
        type: 'grace_period_ending',
        userId,
        metadata: {
          planName,
          gracePeriodEnd,
          hoursRemaining,
          userName
        }
      });

    } catch (error) {
      console.error('Error enviando recordatorio de período de gracia:', error);
      return false;
    }
  }

  /**
   * Función base para enviar emails con reintentos y manejo de errores
   * NOTA: Implementar con tu proveedor de email preferido (SendGrid, Resend, etc.)
   */
  private static async sendEmail(notification: EmailNotification, retryCount: number = 0): Promise<boolean> {
    const maxRetries = 3;
    const retryDelay = Math.pow(2, retryCount) * 1000; // Backoff exponencial: 1s, 2s, 4s

    let notificationId: string | null = null;

    try {
      console.log(`📧 Enviando email (intento ${retryCount + 1}/${maxRetries + 1}):`, {
        to: notification.to,
        subject: notification.subject,
        type: notification.type
      });

      // Registrar notificación como 'pending' antes del envío
      notificationId = await this.logEmailNotification(notification, 'pending');

      // TODO: Implementar con tu proveedor de email real
      // Ejemplo con Resend:
      /*
      const resend = new Resend(process.env.RESEND_API_KEY);
      const result = await resend.emails.send({
        from: 'OposI <<EMAIL>>',
        to: notification.to,
        subject: notification.subject,
        html: notification.htmlContent,
        text: notification.textContent,
      });

      if (result.error) {
        throw new Error(`Resend API error: ${result.error.message}`);
      }
      */

      // Simulación de envío (remover cuando implementes proveedor real)
      await new Promise(resolve => setTimeout(resolve, 100));

      // Simular fallo ocasional para testing (remover en producción)
      if (Math.random() < 0.1 && retryCount === 0) {
        throw new Error('Simulated email provider error');
      }

      // Actualizar estado a 'sent' si el envío fue exitoso
      if (notificationId) {
        await this.updateEmailNotificationStatus(notificationId, 'sent');
      }

      console.log('✅ Email enviado exitosamente');
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error enviando email (intento ${retryCount + 1}):`, errorMessage);

      // Actualizar estado a 'failed' si tenemos el ID
      if (notificationId) {
        await this.updateEmailNotificationStatus(
          notificationId,
          'failed',
          errorMessage
        );
      }

      // Intentar reenvío si no hemos alcanzado el máximo de reintentos
      if (retryCount < maxRetries) {
        console.log(`🔄 Reintentando envío en ${retryDelay}ms...`);

        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return this.sendEmail(notification, retryCount + 1);
      }

      // Si agotamos los reintentos, registrar fallo final
      console.error(`💥 Fallo definitivo después de ${maxRetries + 1} intentos`);
      return false;
    }
  }

  /**
   * Registrar notificación en base de datos para tracking
   */
  private static async logEmailNotification(
    notification: EmailNotification,
    status: string = 'sent'
  ): Promise<string | null> {
    try {
      const insertData: any = {
        recipient_email: notification.to,
        subject: notification.subject,
        type: notification.type,
        sent_at: new Date().toISOString(),
        status: status
      };

      // Agregar user_id si está disponible
      if (notification.userId) {
        insertData.user_id = notification.userId;
      }

      // Agregar metadata si está disponible
      if (notification.metadata) {
        insertData.metadata = notification.metadata;
      }

      const { data, error } = await supabaseAdmin
        .from('email_notifications')
        .insert(insertData)
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      console.log('📝 Email notification logged:', {
        id: data.id,
        type: notification.type,
        recipient: notification.to,
        status: status,
        userId: notification.userId || 'N/A'
      });

      return data.id;
    } catch (error) {
      console.error('Error logging email notification:', error);
      // No lanzar error, es solo para tracking
      return null;
    }
  }

  /**
   * Actualizar estado de una notificación existente
   */
  private static async updateEmailNotificationStatus(
    notificationId: string,
    status: string,
    errorMessage?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        status: status,
        updated_at: new Date().toISOString()
      };

      // Si es un fallo, agregar el mensaje de error a metadata
      if (status === 'failed' && errorMessage) {
        updateData.metadata = {
          error_message: errorMessage,
          failed_at: new Date().toISOString()
        };
      }

      // Si es exitoso, marcar como entregado
      if (status === 'sent') {
        updateData.delivered_at = new Date().toISOString();
      }

      const { error } = await supabaseAdmin
        .from('email_notifications')
        .update(updateData)
        .eq('id', notificationId);

      if (error) {
        throw error;
      }

      console.log('📝 Email notification status updated:', {
        id: notificationId,
        status: status,
        error: errorMessage || 'N/A'
      });

    } catch (error) {
      console.error('Error updating email notification status:', error);
      // No lanzar error, es solo para tracking
    }
  }

  /**
   * Obtener historial de notificaciones de un usuario
   */
  static async getUserNotifications(
    userId: string,
    limit: number = 50,
    type?: string
  ): Promise<{
    notifications: any[];
    total: number;
  }> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('sent_at', { ascending: false });

      if (type) {
        query = query.eq('type', type);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      // Obtener total de notificaciones
      let countQuery = supabaseAdmin
        .from('email_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (type) {
        countQuery = countQuery.eq('type', type);
      }

      const { count } = await countQuery;

      return {
        notifications: notifications || [],
        total: count || 0
      };

    } catch (error) {
      console.error('Error obteniendo notificaciones del usuario:', error);
      return {
        notifications: [],
        total: 0
      };
    }
  }

  /**
   * Obtener estadísticas de notificaciones por tipo
   */
  static async getNotificationStats(
    startDate?: string,
    endDate?: string
  ): Promise<{
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    total: number;
    recentNotifications: any[];
  }> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*');

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      const byType = (notifications || []).reduce((acc, notif) => {
        acc[notif.type] = (acc[notif.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const byStatus = (notifications || []).reduce((acc, notif) => {
        acc[notif.status] = (acc[notif.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const recentNotifications = (notifications || [])
        .sort((a, b) => new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime())
        .slice(0, 10);

      return {
        byType,
        byStatus,
        total: notifications?.length || 0,
        recentNotifications
      };

    } catch (error) {
      console.error('Error obteniendo estadísticas de notificaciones:', error);
      return {
        byType: {},
        byStatus: {},
        total: 0,
        recentNotifications: []
      };
    }
  }

  /**
   * Obtener estadísticas de fallos y errores
   */
  static async getFailureStats(
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalFailures: number;
    failureRate: number;
    errorsByType: Record<string, number>;
    recentFailures: any[];
  }> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('status', 'failed');

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: failures, error } = await query;

      if (error) {
        throw error;
      }

      // Obtener total de notificaciones para calcular tasa de fallo
      let totalQuery = supabaseAdmin
        .from('email_notifications')
        .select('*', { count: 'exact', head: true });

      if (startDate) {
        totalQuery = totalQuery.gte('sent_at', startDate);
      }

      if (endDate) {
        totalQuery = totalQuery.lte('sent_at', endDate);
      }

      const { count: totalCount } = await totalQuery;

      // Agrupar errores por tipo
      const errorsByType = (failures || []).reduce((acc, failure) => {
        const errorMessage = failure.metadata?.error_message || 'Unknown error';
        const errorType = this.categorizeError(errorMessage);
        acc[errorType] = (acc[errorType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const totalFailures = failures?.length || 0;
      const failureRate = totalCount && totalCount > 0 ? (totalFailures / totalCount) * 100 : 0;

      return {
        totalFailures,
        failureRate: Math.round(failureRate * 100) / 100, // Redondear a 2 decimales
        errorsByType,
        recentFailures: (failures || [])
          .sort((a, b) => new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime())
          .slice(0, 10)
          .map(failure => ({
            id: failure.id,
            type: failure.type,
            recipient: failure.recipient_email,
            error: failure.metadata?.error_message || 'Unknown error',
            failedAt: failure.metadata?.failed_at || failure.sent_at
          }))
      };

    } catch (error) {
      console.error('Error obteniendo estadísticas de fallos:', error);
      return {
        totalFailures: 0,
        failureRate: 0,
        errorsByType: {},
        recentFailures: []
      };
    }
  }

  /**
   * Categorizar errores para estadísticas
   */
  private static categorizeError(errorMessage: string): string {
    const message = errorMessage.toLowerCase();

    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return 'Network Error';
    }

    if (message.includes('invalid') || message.includes('malformed') || message.includes('email')) {
      return 'Invalid Email';
    }

    if (message.includes('rate limit') || message.includes('quota') || message.includes('limit')) {
      return 'Rate Limit';
    }

    if (message.includes('auth') || message.includes('key') || message.includes('permission')) {
      return 'Authentication Error';
    }

    if (message.includes('bounce') || message.includes('reject')) {
      return 'Email Bounced';
    }

    return 'Other Error';
  }

  /**
   * Reenviar notificaciones fallidas
   */
  static async retryFailedNotifications(
    maxAge: number = 24, // Máximo 24 horas de antigüedad
    limit: number = 10   // Máximo 10 reintentos por ejecución
  ): Promise<{
    attempted: number;
    successful: number;
    failed: number;
    errors: string[];
  }> {
    try {
      console.log(`🔄 Buscando notificaciones fallidas para reintentar (máximo ${maxAge} horas)...`);

      const cutoffDate = new Date(Date.now() - maxAge * 60 * 60 * 1000).toISOString();

      // Buscar notificaciones fallidas recientes
      const { data: failedNotifications, error } = await supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('status', 'failed')
        .gte('sent_at', cutoffDate)
        .limit(limit);

      if (error) {
        throw error;
      }

      if (!failedNotifications || failedNotifications.length === 0) {
        console.log('✅ No se encontraron notificaciones fallidas para reintentar');
        return { attempted: 0, successful: 0, failed: 0, errors: [] };
      }

      console.log(`📋 Encontradas ${failedNotifications.length} notificaciones para reintentar`);

      let successful = 0;
      let failed = 0;
      const errors: string[] = [];

      // Reintentar cada notificación
      for (const notification of failedNotifications) {
        try {
          const emailNotification: EmailNotification = {
            to: notification.recipient_email,
            subject: notification.subject,
            htmlContent: '', // Necesitaríamos regenerar el contenido
            textContent: '',
            type: notification.type,
            userId: notification.user_id,
            metadata: notification.metadata
          };

          // Marcar como reintento en metadata
          emailNotification.metadata = {
            ...emailNotification.metadata,
            retry_attempt: true,
            original_notification_id: notification.id,
            retry_at: new Date().toISOString()
          };

          const success = await this.sendEmail(emailNotification);

          if (success) {
            successful++;
            // Marcar la notificación original como reintentada exitosamente
            await this.updateEmailNotificationStatus(
              notification.id,
              'retried_successfully',
              'Successfully retried'
            );
          } else {
            failed++;
            errors.push(`Failed to retry notification ${notification.id}`);
          }

        } catch (retryError) {
          failed++;
          const errorMsg = `Error retrying notification ${notification.id}: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      console.log(`🎯 Reintentos completados: ${successful} exitosos, ${failed} fallidos`);

      return {
        attempted: failedNotifications.length,
        successful,
        failed,
        errors
      };

    } catch (error) {
      console.error('❌ Error en retryFailedNotifications:', error);
      throw error;
    }
  }
}
