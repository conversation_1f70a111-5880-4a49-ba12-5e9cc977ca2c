# OposI v7 - Guía de Instalación y Despliegue

## 📋 Requisitos Previos

### Software Necesario:
- **Node.js** (versión 18.0 o superior)
- **npm** (incluido con Node.js)
- **Git** (opcional, para control de versiones)

### Servicios Externos:
- **Supabase** (Base de datos y autenticación)
- **OpenAI API** (Para funcionalidades de IA)
- **Stripe** (Para pagos y suscripciones)

## 🚀 Instalación

### 1. Extraer el Proyecto
```bash
# Extraer el archivo ZIP
# Navegar al directorio del proyecto
cd OposI-v7
```

### 2. Instalar Dependencias
```bash
npm install
```

### 3. Configurar Variables de Entorno

Crear un archivo `.env.local` en la raíz del proyecto con las siguientes variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=tu_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=tu_openai_api_key

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=tu_stripe_publishable_key
STRIPE_SECRET_KEY=tu_stripe_secret_key
STRIPE_WEBHOOK_SECRET=tu_stripe_webhook_secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Configurar Base de Datos (Supabase)

#### Tablas Principales:
- `user_profiles` - Perfiles de usuario
- `documentos` - Documentos subidos
- `flashcards` - Flashcards generadas
- `tests` - Tests generados
- `mapas_mentales` - Mapas mentales
- `conversaciones` - Conversaciones del AI Tutor
- `stripe_transactions` - Transacciones de Stripe
- `user_plan_history` - Historial de cambios de plan
- `feature_access_log` - Log de acceso a características

#### Row Level Security (RLS):
Asegúrate de que RLS esté habilitado en todas las tablas con políticas apropiadas.

### 5. Ejecutar en Desarrollo
```bash
npm run dev
```

La aplicación estará disponible en `http://localhost:3000`

## 🏗️ Estructura del Proyecto

```
OposI-v7/
├── src/
│   ├── app/                 # App Router de Next.js
│   ├── components/          # Componentes reutilizables
│   ├── features/           # Características específicas
│   ├── lib/                # Utilidades y servicios
│   ├── hooks/              # Custom hooks
│   ├── contexts/           # React contexts
│   └── config/             # Configuraciones
├── public/                 # Archivos estáticos
├── __tests__/              # Tests
└── docs/                   # Documentación
```

## 🔧 Configuraciones Importantes

### Middleware de Seguridad
- Content Security Policy configurado para D3.js y Stripe
- Autenticación automática en rutas protegidas
- Validación de planes y permisos

### Planes de Suscripción
- **Free**: Límites básicos, funcionalidades restringidas
- **Usuario**: Acceso completo con límites de tokens
- **Pro**: Acceso completo con características avanzadas

### Funcionalidades Principales
- 📄 Gestión de documentos (PDF/TXT)
- 🤖 AI Tutor con chat inteligente
- 📚 Generación de flashcards con spaced repetition
- 📝 Generación de tests personalizados
- 🧠 Mapas mentales interactivos con D3.js
- 📊 Estadísticas y análisis de progreso
- 💳 Sistema de pagos con Stripe

## 🚀 Despliegue en Producción

### Vercel (Recomendado)
```bash
# Instalar Vercel CLI
npm i -g vercel

# Desplegar
vercel

# Configurar variables de entorno en Vercel Dashboard
```

### Variables de Entorno en Producción
Asegúrate de configurar todas las variables de entorno en tu plataforma de despliegue.

### Webhooks de Stripe
Configurar webhook endpoint: `https://tu-dominio.com/api/webhooks/stripe`

## 🔒 Seguridad

### Configuraciones Implementadas:
- Row Level Security en Supabase
- Validación de permisos por plan
- Content Security Policy
- Sanitización de inputs
- Rate limiting en APIs

### Recomendaciones:
- Usar HTTPS en producción
- Configurar CORS apropiadamente
- Monitorear logs de seguridad
- Actualizar dependencias regularmente

## 📞 Soporte

Para problemas técnicos o preguntas sobre la implementación, consulta:
- Documentación de Next.js
- Documentación de Supabase
- Documentación de Stripe
- Logs de la aplicación

## 🔄 Actualizaciones

Para actualizar la aplicación:
1. Hacer backup de la base de datos
2. Actualizar el código
3. Ejecutar migraciones si es necesario
4. Probar en entorno de desarrollo
5. Desplegar en producción

---

**Versión**: 7.0
**Última actualización**: Enero 2025
**Framework**: Next.js 15 + React 18
**Base de datos**: Supabase (PostgreSQL)
**Autenticación**: Supabase Auth
**Pagos**: Stripe
**IA**: OpenAI GPT-4
