// src/lib/services/userManagement.ts
// Servicio para gestión automatizada de usuarios y perfiles

import { SupabaseAdminService, ExtendedUserProfile } from '@/lib/supabase/admin';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/lib/utils/planLimits';

export interface CreateUserRequest {
  email: string;
  name?: string;
  planId: string;
  stripeSessionId: string;
  stripeCustomerId?: string;
  amount: number;
  currency: string;
  subscriptionId?: string;
}

export interface UserActivationResult {
  success: boolean;
  userId?: string;
  profileId?: string;
  transactionId?: string;
  error?: string;
}

export class UserManagementService {
  
  /**
   * Crear usuario completo con perfil y transacción
   */
  static async createUserWithPlan(request: CreateUserRequest): Promise<UserActivationResult> {
    try {
      console.log('🚀 Iniciando creación de usuario:', request.email);
      
      // 1. Validar plan
      const planConfig = getPlanConfiguration(request.planId);
      if (!planConfig) {
        throw new Error(`Plan inválido: ${request.planId}`);
      }
      
      // 2. Verificar si ya existe una transacción para esta sesión
      const existingTransaction = await SupabaseAdminService.getTransactionBySessionId(request.stripeSessionId);
      if (existingTransaction) {
        console.log('⚠️ Transacción ya existe:', existingTransaction.id);
        return {
          success: false,
          error: 'Transacción ya procesada'
        };
      }
      
      // 3. Crear registro de transacción
      const transaction = await SupabaseAdminService.createStripeTransaction({
        stripe_session_id: request.stripeSessionId,
        stripe_customer_id: request.stripeCustomerId,
        user_email: request.email,
        user_name: request.name,
        plan_id: request.planId,
        amount: request.amount,
        currency: request.currency,
        payment_status: 'paid',
        subscription_id: request.subscriptionId,
        metadata: {
          created_by: 'webhook',
          plan_name: planConfig.name
        }
      });
      
      console.log('✅ Transacción creada:', transaction.id);
      
      // 4. Crear invitación de usuario
      const userData = {
        name: request.name,
        plan: request.planId,
        stripe_session_id: request.stripeSessionId,
        stripe_customer_id: request.stripeCustomerId,
        transaction_id: transaction.id,
        payment_verified: true
      };
      
      const userInvitation = await SupabaseAdminService.createUserWithInvitation(
        request.email,
        userData
      );
      
      console.log('✅ Invitación de usuario creada:', userInvitation.user?.id);
      
      // 5. Crear perfil de usuario
      const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
      const profile: Partial<ExtendedUserProfile> = {
        user_id: userInvitation.user!.id,
        subscription_plan: request.planId as 'free' | 'usuario' | 'pro',
        monthly_token_limit: getTokenLimitForPlan(request.planId),
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: true,
        stripe_customer_id: request.stripeCustomerId,
        last_payment_date: new Date().toISOString(),
        auto_renew: request.subscriptionId ? true : false,
        plan_features: planConfig.features,
        security_flags: {
          created_via_webhook: true,
          payment_method: 'stripe',
          activation_date: new Date().toISOString()
        }
      };
      
      const userProfile = await SupabaseAdminService.upsertUserProfile(profile);
      console.log('✅ Perfil de usuario creado:', userProfile.id);
      
      // 6. Registrar cambio de plan
      await SupabaseAdminService.logPlanChange({
        user_id: userInvitation.user!.id,
        old_plan: undefined,
        new_plan: request.planId,
        changed_by: 'system',
        reason: 'Initial plan assignment via payment',
        transaction_id: transaction.id
      });
      
      // 7. Actualizar transacción con user_id y marcar como activada
      await SupabaseAdminService.updateTransactionWithUser(transaction.id, userInvitation.user!.id);
      await SupabaseAdminService.activateTransaction(transaction.id);
      
      console.log('🎉 Usuario creado exitosamente:', {
        userId: userInvitation.user!.id,
        profileId: userProfile.id,
        transactionId: transaction.id
      });
      
      return {
        success: true,
        userId: userInvitation.user!.id,
        profileId: userProfile.id,
        transactionId: transaction.id
      };
      
    } catch (error) {
      console.error('❌ Error creando usuario:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }
  
  /**
   * Actualizar plan de usuario existente
   */
  static async updateUserPlan(
    userId: string, 
    newPlanId: string, 
    transactionId?: string,
    reason: string = 'Plan upgrade/downgrade'
  ): Promise<UserActivationResult> {
    try {
      console.log('🔄 Actualizando plan de usuario:', userId, 'a', newPlanId);
      
      // 1. Obtener perfil actual
      const currentProfile = await SupabaseAdminService.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('Usuario no encontrado');
      }
      
      // 2. Validar nuevo plan
      const planConfig = getPlanConfiguration(newPlanId);
      if (!planConfig) {
        throw new Error(`Plan inválido: ${newPlanId}`);
      }
      
      // 3. Actualizar perfil
      const updatedProfile: Partial<ExtendedUserProfile> = {
        ...currentProfile,
        subscription_plan: newPlanId as 'free' | 'usuario' | 'pro',
        monthly_token_limit: getTokenLimitForPlan(newPlanId),
        last_payment_date: new Date().toISOString(),
        plan_features: planConfig.features,
        updated_at: new Date().toISOString()
      };
      
      const profile = await SupabaseAdminService.upsertUserProfile(updatedProfile);
      
      // 4. Registrar cambio de plan
      await SupabaseAdminService.logPlanChange({
        user_id: userId,
        old_plan: currentProfile.subscription_plan,
        new_plan: newPlanId,
        changed_by: 'system',
        reason,
        transaction_id: transactionId
      });
      
      console.log('✅ Plan actualizado exitosamente');
      
      return {
        success: true,
        userId,
        profileId: profile.id
      };
      
    } catch (error) {
      console.error('❌ Error actualizando plan:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }
  
  /**
   * Verificar estado de pago de usuario
   */
  static async verifyUserPaymentStatus(userId: string): Promise<{
    verified: boolean;
    plan: string;
    expiresAt?: string;
    lastPayment?: string;
  }> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return { verified: false, plan: 'none' };
      }
      
      return {
        verified: profile.payment_verified,
        plan: profile.subscription_plan,
        expiresAt: profile.plan_expires_at || undefined,
        lastPayment: profile.last_payment_date || undefined
      };
      
    } catch (error) {
      console.error('Error verificando estado de pago:', error);
      return { verified: false, plan: 'error' };
    }
  }
  
  /**
   * Obtener estadísticas de usuarios
   */
  static async getUserStats(): Promise<{
    total: number;
    byPlan: Record<string, number>;
    verified: number;
    unverified: number;
  }> {
    try {
      // Esta función requeriría consultas más complejas
      // Por ahora retornamos estructura básica
      return {
        total: 0,
        byPlan: { free: 0, usuario: 0, pro: 0 },
        verified: 0,
        unverified: 0
      };
    } catch (error) {
      console.error('Error obteniendo estadísticas:', error);
      throw error;
    }
  }
}
