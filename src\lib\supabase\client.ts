import { createBrowserClient } from '@supabase/ssr';

// Cliente para el navegador (componentes del cliente)
export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true, // No persistir la sesión automáticamente
        autoRefreshToken: true, // No refrescar el token automáticamente
        detectSessionInUrl: true // No detectar sesión en URL
      }
    }
  );
}

// Mantener compatibilidad con código existente
export const supabase = createClient();
