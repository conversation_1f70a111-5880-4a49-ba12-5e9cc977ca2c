// src/features/profile/components/PlanUsage.tsx
// Componente para mostrar el uso actual y límites del plan

'use client';

import React from 'react';
import {
  FiFileText,
  FiCheckSquare,
  FiLayers,
  FiTarget, // Cambiado de FiBrain a FiTarget para mapas mentales
  FiCpu,
  FiTrendingUp,
  FiAlertTriangle,
  FiCheck
} from 'react-icons/fi';

interface UserProfile {
  user: {
    id: string;
    email: string;
    name: string;
    created_at: string;
  };
  profile: {
    subscription_plan: string;
    plan_expires_at?: string;
    auto_renew: boolean;
    payment_verified: boolean;
    last_payment_date?: string;
  };
  access: {
    plan: string;
    features: string[];
    limits: any;
    currentUsage: any;
    paymentVerified: boolean;
  };
  tokenUsage: {
    current: number;
    limit: number;
    percentage: number;
    remaining: number;
  };
}

interface PlanUsageProps {
  userProfile: UserProfile;
}

interface UsageItem {
  icon: React.ComponentType<any>;
  label: string;
  current: number;
  limit: number | string;
  color: string;
  description: string;
}

export default function PlanUsage({ userProfile }: PlanUsageProps) {
  const { access, tokenUsage, profile } = userProfile;

  // Validaciones defensivas
  if (!access || !tokenUsage || !profile) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">Cargando información del plan...</p>
        </div>
      </div>
    );
  }

  // Calcular porcentaje de uso
  const getUsagePercentage = (current: number, limit: number | string): number => {
    if (typeof limit === 'string' || limit === 0) return 0;
    return Math.min((current / limit) * 100, 100);
  };

  // Obtener color basado en el porcentaje de uso
  const getUsageColor = (percentage: number): string => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    if (percentage >= 50) return 'bg-blue-500';
    return 'bg-green-500';
  };

  // Obtener color del texto basado en el porcentaje
  const getTextColor = (percentage: number): string => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const usageItems: UsageItem[] = [
    {
      icon: FiFileText,
      label: 'Documentos',
      current: access.currentUsage?.documents || 0,
      limit: access.limits?.documents || 'Ilimitado',
      color: 'blue',
      description: 'Documentos PDF/TXT subidos'
    },
    {
      icon: FiCheckSquare,
      label: 'Tests',
      current: access.currentUsage?.tests || 0,
      limit: access.limits?.tests || 'Ilimitado',
      color: 'green',
      description: 'Tests generados este mes'
    },
    {
      icon: FiLayers,
      label: 'Flashcards',
      current: access.currentUsage?.flashcards || 0,
      limit: access.limits?.flashcards || 'Ilimitado',
      color: 'purple',
      description: 'Flashcards creadas este mes'
    },
    {
      icon: FiTarget, // Cambiado de FiBrain a FiTarget para mapas mentales
      label: 'Mapas Mentales',
      current: access.currentUsage?.mindMaps || 0,
      limit: access.limits?.mindMaps || 'Ilimitado',
      color: 'indigo',
      description: 'Mapas mentales generados este mes'
    }
  ];

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getPlanDisplayName = (plan: string) => {
    switch (plan) {
      case 'free': return 'Plan Gratuito';
      case 'usuario': return 'Plan Usuario';
      case 'pro': return 'Plan Pro';
      default: return plan;
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Uso y Límites del Plan</h2>
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          profile.subscription_plan === 'free' 
            ? 'bg-gray-100 text-gray-800'
            : profile.subscription_plan === 'usuario'
            ? 'bg-blue-100 text-blue-800'
            : 'bg-purple-100 text-purple-800'
        }`}>
          {getPlanDisplayName(profile.subscription_plan)}
        </span>
      </div>

      {/* Uso de Tokens */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <FiCpu className="w-6 h-6 text-blue-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Tokens de IA</h3>
              <p className="text-sm text-gray-600">Uso mensual de procesamiento de IA</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatNumber(tokenUsage.current || 0)}
            </div>
            <div className="text-sm text-gray-600">
              de {formatNumber(tokenUsage.limit || 0)}
            </div>
          </div>
        </div>

        {/* Barra de progreso de tokens */}
        <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
          <div
            className={`h-3 rounded-full transition-all duration-300 ${getUsageColor(tokenUsage.percentage || 0)}`}
            style={{ width: `${tokenUsage.percentage || 0}%` }}
          ></div>
        </div>

        <div className="flex justify-between items-center text-sm">
          <span className={getTextColor(tokenUsage.percentage || 0)}>
            {(tokenUsage.percentage || 0).toFixed(1)}% utilizado
          </span>
          <span className="text-gray-600">
            {formatNumber(tokenUsage.remaining || 0)} restantes
          </span>
        </div>

        {(tokenUsage.percentage || 0) >= 90 && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
            <FiAlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">
              Te estás acercando al límite de tokens. Considera actualizar tu plan.
            </span>
          </div>
        )}
      </div>

      {/* Uso de Funciones */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {usageItems.map((item) => {
          const Icon = item.icon;
          const percentage = getUsagePercentage(item.current, item.limit as number);
          const isUnlimited = typeof item.limit === 'string';

          // Validación defensiva para el icono
          if (!Icon || typeof Icon !== 'function') {
            console.error('Icon is invalid for item:', item.label, Icon);
            return (
              <div key={item.label} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className="w-5 h-5 bg-gray-300 rounded mr-2"></div>
                    <span className="font-medium text-gray-900">{item.label}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">
                      {item.current}
                    </div>
                    <div className="text-xs text-gray-500">
                      {isUnlimited ? 'Ilimitado' : `de ${item.limit}`}
                    </div>
                  </div>
                </div>
                {!isUnlimited && (
                  <>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(percentage)}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-600">
                      {percentage.toFixed(1)}% utilizado
                    </div>
                  </>
                )}
                <p className="text-xs text-gray-500 mt-2">{item.description}</p>
              </div>
            );
          }

          return (
            <div key={item.label} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Icon className={`w-5 h-5 text-${item.color}-600 mr-2`} />
                  <span className="font-medium text-gray-900">{item.label}</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-gray-900">
                    {item.current}
                  </div>
                  <div className="text-xs text-gray-500">
                    {isUnlimited ? 'Ilimitado' : `de ${item.limit}`}
                  </div>
                </div>
              </div>

              {!isUnlimited && (
                <>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(percentage)}`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600">
                    {percentage.toFixed(1)}% utilizado
                  </div>
                </>
              )}

              <p className="text-xs text-gray-500 mt-2">{item.description}</p>
            </div>
          );
        })}
      </div>

      {/* Funciones Disponibles */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Funciones Disponibles</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {(access.features || []).map((feature) => {
            const featureLabels: Record<string, string> = {
              'document_upload': 'Subida de documentos',
              'test_generation': 'Generación de tests',
              'flashcard_generation': 'Creación de flashcards',
              'mind_map_generation': 'Mapas mentales',
              'study_planning': 'Planificación de estudios',
              'ai_tutor': 'Tutor de IA',
              'summaries': 'Resúmenes automáticos',
              'advanced_analytics': 'Análisis avanzado'
            };

            return (
              <div key={feature} className="flex items-center">
                <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-700">
                  {featureLabels[feature] || feature}
                </span>
              </div>
            );
          })}
        </div>

        {profile.subscription_plan === 'free' && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-3">
              ¿Necesitas más funciones y límites más altos?
            </p>
            <a
              href="/upgrade-plan"
              className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
            >
              <FiTrendingUp className="w-4 h-4 mr-2" />
              Actualizar Plan
            </a>
          </div>
        )}
      </div>
    </div>
  );
}
