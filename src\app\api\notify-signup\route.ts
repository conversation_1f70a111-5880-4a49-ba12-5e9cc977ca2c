// src/app/api/notify-signup/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const {
      type = 'subscription_request',
      email,
      userEmail,
      customerName,
      userName,
      planName,
      subscriptionPlan,
      periodEnd
    } = await request.json();

    console.log('Using Resend API key starting with:', process.env.RESEND_API_KEY?.substring(0, 10) + '...');
    console.log('Sending notification to:', process.env.NOTIFICATION_EMAIL);

    const finalEmail = email || userEmail;
    const finalName = customerName || userName;
    const finalPlan = planName || subscriptionPlan;

    if (!finalEmail) {
      return NextResponse.json(
        { error: 'Email es requerido' },
        { status: 400 }
      );
    }

    let emailData;

    if (type === 'subscription_cancelled') {
      // Email para cancelación de suscripción
      emailData = {
        from: 'OposiAI Notificaciones <<EMAIL>>',
        to: [process.env.NOTIFICATION_EMAIL!],
        subject: `❌ Suscripción Cancelada OposiAI: ${finalName}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc2626;">Suscripción Cancelada</h2>

            <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #991b1b;">Detalles de la Cancelación</h3>
              <p><strong>Email:</strong> ${finalEmail}</p>
              <p><strong>Nombre:</strong> ${finalName || 'No proporcionado'}</p>
              <p><strong>Plan Cancelado:</strong> ${finalPlan}</p>
              <p><strong>Acceso hasta:</strong> ${periodEnd || 'No especificado'}</p>
              <p><strong>Fecha de Cancelación:</strong> ${new Date().toLocaleString('es-ES')}</p>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin-top: 0; color: #92400e;">Información</h4>
              <p style="margin-bottom: 0;">El usuario mantendrá acceso a las funciones premium hasta el final de su período de facturación.</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Este email fue generado automáticamente por el sistema de suscripciones de OposiAI.
              </p>
            </div>
          </div>
        `,
      };
    } else {
      // Email para solicitud de suscripción (comportamiento original)
      emailData = {
        from: 'OposiAI Notificaciones <<EMAIL>>',
        to: [process.env.NOTIFICATION_EMAIL!],
        subject: `🚀 Nueva Solicitud de Suscripción OposiAI: Plan ${finalPlan}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">Nueva Solicitud de Suscripción</h2>

            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1e40af;">Detalles del Cliente</h3>
              <p><strong>Email:</strong> ${finalEmail}</p>
              <p><strong>Nombre:</strong> ${finalName || 'No proporcionado'}</p>
              <p><strong>Plan Seleccionado:</strong> ${finalPlan}</p>
              <p><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</p>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin-top: 0; color: #92400e;">Acción Requerida</h4>
              <p style="margin-bottom: 0;">Por favor, añade manualmente este usuario a Supabase con el plan correspondiente.</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Este email fue generado automáticamente por el sistema de suscripciones de OposiAI.
              </p>
            </div>
          </div>
        `,
      };
    }

    console.log('Attempting to send email with Resend...');
    console.log('Email payload:', {
      from: emailData.from,
      to: emailData.to,
      subject: emailData.subject
    });

    const data = await resend.emails.send(emailData);

    console.log('Email enviado exitosamente:', data);

    return NextResponse.json({
      success: true,
      message: 'Notificación enviada correctamente',
      emailData: data
    });

  } catch (error) {
    console.error('Error enviando email con Resend:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al enviar la notificación',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
