'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-hot-toast';
import { Documento } from '@/lib/supabase/supabaseClient';
import { guardarResumen, existeResumenParaDocumento } from '@/lib/supabase/resumenesService';

const summarySchema = z.object({
  instrucciones: z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres'),
});

type SummaryFormData = z.infer<typeof summarySchema>;

interface SummaryGeneratorProps {
  documentosSeleccionados: Documento[];
  onSummaryGenerated?: (summaryId: string) => void;
}

export default function SummaryGenerator({ documentosSeleccionados, onSummaryGenerated }: SummaryGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [resumenGenerado, setResumenGenerado] = useState<string | null>(null);
  const [tiempoEstimado, setTiempoEstimado] = useState<number>(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<SummaryFormData>({
    resolver: zodResolver(summarySchema),
    defaultValues: {
      instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'
    }
  });

  // Funciones de validación
  const validarDocumentoParaResumen = (documento: any): { valido: boolean; error?: string } => {
    if (!documento) {
      return { valido: false, error: 'No se ha proporcionado ningún documento' };
    }

    if (!documento.titulo || documento.titulo.trim().length === 0) {
      return { valido: false, error: 'El documento debe tener un título' };
    }

    if (!documento.contenido || documento.contenido.trim().length === 0) {
      return { valido: false, error: 'El documento debe tener contenido' };
    }

    if (documento.contenido.trim().length < 50) {
      return { valido: false, error: 'El contenido del documento es demasiado corto para generar un resumen útil' };
    }

    return { valido: true };
  };

  const estimarTiempoGeneracion = (documento: any): number => {
    if (!documento || !documento.contenido) {
      return 30; // 30 segundos por defecto
    }

    const palabras = documento.contenido.split(/\s+/).length;

    // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos
    const tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));

    return tiempoEstimado;
  };

  // Validaciones
  const puedeGenerar = documentosSeleccionados.length === 1;
  const documento = documentosSeleccionados[0];

  const validacionDocumento = documento ? validarDocumentoParaResumen(documento) : { valido: false, error: 'No hay documento seleccionado' };

  const onSubmit = async (data: SummaryFormData) => {
    if (!puedeGenerar || !documento || !validacionDocumento.valido) {
      toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');
      return;
    }

    try {
      console.log('🚀 Iniciando generación de resumen...');
      setIsGenerating(true);
      setTiempoEstimado(estimarTiempoGeneracion(documento));

      console.log('📄 Documento seleccionado:', {
        id: documento.id,
        titulo: documento.titulo,
        categoria: documento.categoria,
        numero_tema: documento.numero_tema,
        contenidoLength: documento.contenido?.length || 0
      });

      // Verificar si ya existe un resumen para este documento
      console.log('🔍 Verificando si ya existe resumen...');
      const existe = await existeResumenParaDocumento(documento.id);
      if (existe) {
        console.log('⚠️ Ya existe un resumen para este documento');
        toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');
        setIsGenerating(false);
        return;
      }
      console.log('✅ No existe resumen previo, continuando...');

      // Preparar datos para la API
      const requestData = {
        action: 'generarResumen',
        peticion: `${documento.titulo}|${documento.categoria || ''}|${documento.numero_tema || ''}|${data.instrucciones}`,
        contextos: [documento.contenido]
      };

      console.log('📡 Enviando petición a la API:', {
        action: requestData.action,
        peticion: requestData.peticion,
        contextosLength: requestData.contextos.length,
        primerContextoLength: requestData.contextos[0]?.length || 0
      });

      // Generar el resumen usando la API
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log(`📨 Respuesta recibida - Status: ${response.status}, OK: ${response.ok}`);

      // Capturar respuesta como texto primero para depuración
      const responseText = await response.text();
      console.log('📄 Respuesta como texto (primeros 200 chars):', responseText.substring(0, 200));

      // Intentar parsear como JSON
      let result;
      try {
        result = JSON.parse(responseText);
        console.log('✅ JSON parseado exitosamente');
      } catch (parseError) {
        console.error('❌ Error al parsear respuesta JSON:', parseError);
        console.error('📄 Respuesta completa:', responseText);
        throw new Error(`Error en formato de respuesta: ${responseText.substring(0, 100)}...`);
      }

      if (!response.ok) {
        console.error('❌ Error en respuesta de la API:', result);
        throw new Error(result.detalles || result.error || 'Error al generar el resumen');
      }

      console.log('📋 Resultado de la API:', {
        hasResult: !!result.result,
        resultLength: result.result?.length || 0,
        resultPreview: result.result?.substring(0, 100) || 'Sin contenido'
      });

      const resumenContent = result.result;

      if (!resumenContent) {
        console.error('❌ No se recibió contenido del resumen');
        throw new Error('No se recibió contenido del resumen');
      }

      console.log('✅ Contenido del resumen recibido, longitud:', resumenContent.length);
      setResumenGenerado(resumenContent);

      // Guardar en Supabase
      console.log('💾 Guardando resumen en Supabase...');
      const resumenId = await guardarResumen(
        documento.id,
        `Resumen: ${documento.titulo}`,
        resumenContent,
        data.instrucciones
      );

      if (resumenId) {
        console.log('✅ Resumen guardado exitosamente con ID:', resumenId);
        toast.success('Resumen generado y guardado exitosamente');
        onSummaryGenerated?.(resumenId);
        reset();
      } else {
        console.error('❌ Error al guardar el resumen - no se recibió ID');
        throw new Error('Error al guardar el resumen en la base de datos');
      }

    } catch (error) {
      console.error('Error completo al generar resumen:', error);

      // Manejo mejorado de errores con más detalles
      let errorMessage = 'Error al generar el resumen';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        // Si es un objeto, intentar extraer información útil
        const errorObj = error as any;
        if (errorObj.message) {
          errorMessage = errorObj.message;
        } else if (errorObj.error) {
          errorMessage = errorObj.error;
        } else if (errorObj.detalles) {
          errorMessage = errorObj.detalles;
        } else {
          errorMessage = `Error del servidor: ${JSON.stringify(error)}`;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      console.error('Mensaje de error procesado:', errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
      setTiempoEstimado(0);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información del documento seleccionado */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 mb-2">📄 Generación de Resúmenes</h3>
        
        {!puedeGenerar ? (
          <div className="text-red-700">
            <p className="font-medium">⚠️ Selección incorrecta</p>
            <p className="text-sm mt-1">
              {documentosSeleccionados.length === 0 
                ? 'Debes seleccionar exactamente un documento para generar un resumen.'
                : `Tienes ${documentosSeleccionados.length} documentos seleccionados. Solo se permite generar un resumen por tema.`
              }
            </p>
          </div>
        ) : !validacionDocumento.valido ? (
          <div className="text-red-700">
            <p className="font-medium">⚠️ Documento no válido</p>
            <p className="text-sm mt-1">{validacionDocumento.error}</p>
          </div>
        ) : (
          <div className="text-blue-700">
            <p className="font-medium">✅ Documento seleccionado:</p>
            <p className="text-sm mt-1">
              <strong>{documento.titulo}</strong>
              {documento.numero_tema && ` (Tema ${documento.numero_tema})`}
            </p>
            <p className="text-xs mt-1 text-blue-600">
              Contenido: ~{documento.contenido.split(/\s+/).length} palabras
            </p>
          </div>
        )}
      </div>

      {/* Formulario de generación */}
      {puedeGenerar && validacionDocumento.valido && (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="instrucciones" className="block text-gray-700 text-sm font-bold mb-2">
              Instrucciones para el resumen:
            </label>
            <textarea
              id="instrucciones"
              {...register('instrucciones')}
              disabled={isGenerating}
              rows={4}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical"
              placeholder="Describe cómo quieres que sea el resumen..."
            />
            {errors.instrucciones && (
              <span className="text-red-500 text-sm">{errors.instrucciones.message}</span>
            )}
            <p className="text-sm text-gray-500 mt-1">
              Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.
            </p>
          </div>

          <button
            type="submit"
            disabled={isGenerating}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center"
          >
            {isGenerating ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generando resumen...
                {tiempoEstimado > 0 && ` (~${tiempoEstimado}s)`}
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd" />
                  <path fillRule="evenodd" d="M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                Generar Resumen
              </>
            )}
          </button>

          {isGenerating && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-yellow-800 text-sm">
                <strong>⏳ Generando resumen...</strong><br />
                La IA está analizando el contenido y creando un resumen estructurado. 
                Este proceso puede tardar {tiempoEstimado > 0 ? `aproximadamente ${tiempoEstimado} segundos` : 'unos momentos'}.
              </p>
            </div>
          )}
        </form>
      )}

      {/* Vista previa del resumen generado */}
      {resumenGenerado && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">📋 Resumen Generado</h3>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
            <div 
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ 
                __html: resumenGenerado.replace(/\n/g, '<br />') 
              }}
            />
          </div>
          <p className="text-sm text-gray-500 mt-2">
            ✅ Resumen guardado exitosamente. Puedes acceder a él desde la sección de resúmenes.
          </p>
        </div>
      )}
    </div>
  );
}


