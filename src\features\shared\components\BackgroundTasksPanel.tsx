'use client';

import React, { useState } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';
import { 
  ChevronDownIcon, 
  ChevronUpIcon, 
  XMarkIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

const BackgroundTasksPanel: React.FC = () => {
  const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = useBackgroundTasks();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showCompleted, setShowCompleted] = useState(false);

  const totalTasks = activeTasks.length + completedTasks.length;

  if (totalTasks === 0) {
    return null;
  }

  const getTaskIcon = (task: BackgroundTask) => {
    switch (task.status) {
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <Cog6ToothIcon className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'error':
        return <ExclamationCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTaskTypeLabel = (type: BackgroundTask['type']) => {
    switch (type) {
      case 'mapa-mental':
        return 'Mapa Mental';
      case 'test':
        return 'Test';
      case 'flashcards':
        return 'Flashcards';
      default:
        return 'Tarea';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('es-ES', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      {/* Panel principal */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        {/* Header */}
        <div 
          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center space-x-2">
            <Cog6ToothIcon className="h-5 w-5" />
            <span className="font-medium">
              Tareas ({activeTasks.length} activas)
            </span>
          </div>
          {isExpanded ? (
            <ChevronDownIcon className="h-5 w-5" />
          ) : (
            <ChevronUpIcon className="h-5 w-5" />
          )}
        </div>

        {/* Contenido expandible */}
        {isExpanded && (
          <div className="max-h-96 overflow-y-auto">
            {/* Tareas activas */}
            {activeTasks.length > 0 && (
              <div className="p-3 border-b border-gray-100">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">
                  Tareas Activas
                </h4>
                <div className="space-y-2">
                  {activeTasks.map((task) => (
                    <div 
                      key={task.id}
                      className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md"
                    >
                      {getTaskIcon(task)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {getTaskTypeLabel(task.type)}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {task.title}
                        </p>
                        {task.progress !== undefined && (
                          <div className="mt-1">
                            <div className="bg-gray-200 rounded-full h-1.5">
                              <div 
                                className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                                style={{ width: `${task.progress}%` }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                      <span className="text-xs text-gray-400">
                        {formatTime(task.createdAt)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tareas completadas */}
            {completedTasks.length > 0 && (
              <div className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <button
                    onClick={() => setShowCompleted(!showCompleted)}
                    className="text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1"
                  >
                    <span>Completadas ({completedTasks.length})</span>
                    {showCompleted ? (
                      <ChevronUpIcon className="h-4 w-4" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4" />
                    )}
                  </button>
                  {completedTasks.length > 0 && (
                    <button
                      onClick={clearCompletedTasks}
                      className="text-xs text-gray-500 hover:text-red-600 transition-colors"
                    >
                      Limpiar
                    </button>
                  )}
                </div>

                {showCompleted && (
                  <div className="space-y-2">
                    {completedTasks.map((task) => (
                      <div 
                        key={task.id}
                        className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md"
                      >
                        {getTaskIcon(task)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {getTaskTypeLabel(task.type)}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {task.title}
                          </p>
                          {task.error && (
                            <p className="text-xs text-red-500 truncate">
                              {task.error}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-400">
                            {task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)}
                          </span>
                          <button
                            onClick={() => removeTask(task.id)}
                            className="text-gray-400 hover:text-red-500 transition-colors"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BackgroundTasksPanel;
